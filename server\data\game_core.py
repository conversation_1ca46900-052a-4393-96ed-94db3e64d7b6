
"""
游戏业务逻辑核心模块
"""
from asyncio import Queue, Lock
from copy import deepcopy
from email import message
import random
from typing import Dict, Any, Optional, Callable
from urllib import response
from .database import DatabaseManager


class GameConfig:
    """游戏基础配置数据"""
    
    # 游戏区域坐标
    AREA_0 = [((45 * 6 + 13) + i * 81, 45 * 6 + 81 * 5 + 7 + j * 45) for i in range(5) for j in range(6)]
    AREA_1 = [(45 * 6 + 81 * 5 - 5 + i * 45, 45 * 6 + 25 + j * 81) for i in range(6) for j in range(5)]
    AREA_2 = [((45 * 6 + 13) + i * 81, 7 + j * 45) for i in range(5) for j in range(6)]
    AREA_3 = [(-5 + i * 45, 45 * 6 + 25 + j * 81) for i in range(6) for j in range(5)]
    AREA_MIDDLE = [(283 + i * 162, 295 + j * 162) for i in range(3) for j in range(3)]
    
    # 所有区域合并
    ALL_AREAS = AREA_0 + AREA_1 + AREA_2 + AREA_3 + AREA_MIDDLE
    
    # 军营坐标
    BARRACKS = [
        (364, 727), (526, 727), (445, 772), (364, 817), (526, 817),
        (85, 376), (175, 376), (130, 457), (85, 538), (175, 538),
        (364, 97), (526, 97), (445, 142), (364, 187), (526, 187),
        (715, 376), (805, 376), (760, 457), (715, 538), (805, 538)
    ]
    
    # 总部坐标
    HEADQUARTERS = [
        (364, 907), (526, 907), (895, 376), (895, 538),
        (364, 7), (526, 7), (-5, 376), (-5, 538)
    ]
    
    # 死亡区域坐标
    AREA_0_DEAD = [(685 + i * 65, 682 + j * 45) for i in range(4) for j in range(6)]
    AREA_1_DEAD = [(670 + i * 45, 217 - j * 65) for j in range(4) for i in range(6)]
    AREA_2_DEAD = [(205 - i * 65, 232 - j * 45) for i in range(4) for j in range(6)]
    AREA_3_DEAD = [(220 - i * 45, 697 + j * 65) for j in range(4) for i in range(6)]
    
    # 棋子等级
    PIECE_RANKS = ["11", "10", "09", "08", "07", "06", "05", "04", "03", "02", "01", "00"]
    
    @classmethod
    def get_area_list(cls, area_id: int) -> list:
        """获取指定的区域坐标
                
        Args:
            area_id: 区域ID　-> 0-3: 0-3号区域; 4: 中间区域; 5: 军营; 6: 总部; 7: 所有区域
        """
        areas = [cls.AREA_0, cls.AREA_1, cls.AREA_2, cls.AREA_3]
        if 0 <= area_id < 4:
            return areas[area_id]
        elif area_id == 4:
            return cls.AREA_MIDDLE
        elif area_id == 5:
            return cls.BARRACKS
        elif area_id == 6:
            return cls.HEADQUARTERS
        else:
            return cls.ALL_AREAS
    
    @classmethod
    def get_dead_area_list(cls, area_id: int) -> list:
        """获取指定玩家的死亡区域坐标
        
        Args:
            player_id: 死亡区域ID -> 0-3: 0-3号玩家
        """
        dead_areas = [cls.AREA_0_DEAD, cls.AREA_1_DEAD, cls.AREA_2_DEAD, cls.AREA_3_DEAD]
        if 0 <= area_id < 4:
            return dead_areas[area_id]
        return []
    
    @classmethod
    def get_rank_list(cls) -> list:
        """获取棋子等级列表"""
        return cls.PIECE_RANKS



class GameCore:
    """游戏业务逻辑核心
    
    负责处理所有游戏相关的业务逻辑：
    - 游戏房间创建、加入、离开
    - 游戏会话管理
    - 游戏状态处理
    - 游戏历史记录
    """
    
    def __init__(self, format_data: Callable, db_manager: DatabaseManager, logger, response_queue: Queue):
        """初始化游戏核心模块
        
        Args:
            db_manager: 数据库管理器
            logger: 日志记录器
        """
        self.format_data = format_data
        self.db_manager = db_manager
        self.logger = logger
        self.response_queue = response_queue

        self.room_to_game = {}  # {room_name: game} 房间名到游戏对象的映射
    
    async def cleanup(self):
        """清理资源"""
        self.room_to_game.clear()
        self.logger.info("游戏核心模块资源清理完成")
    
    
    # ==================== 消息处理器 ====================

    async def handle_game_event(self, data_list: list):
        """处理游戏事件"""
        if data_list[1].get('type') == 'init_game':
            return await self.handle_init_game(data_list)
        elif data_list[1].get('type') == 'pick_pieces':
            return await self.handle_pick_pieces(data_list)

    
    async def handle_init_game(self, data_list: list):
        """处理开始游戏"""
        seats_status = data_list[1].get('seats_status')
        game_room = data_list[1].get('room_name')
        broadcast_room = data_list[5]
        members = [i[0] for i in seats_status]
        result = await self.db_manager.create_game_session(game_room, members)
        # 如果返回失败，则返回错误信息
        if not result['success']:
            self.logger.error(f"创建游戏会话失败: {result['error']}")
            return None
        session_code = result['session_code']
        self.logger.info(f'游戏session已创建: {session_code}')

        # 创建游戏对象,管理每个游戏的状态和数据
        game = Game(session_code, game_room, members, self.db_manager, self.logger)
        self.room_to_game[game_room] = game
        self.logger.info(f"游戏对象已添加到房间到游戏对象的映射中: {self.room_to_game}")

        # 生成棋子数据，并插入数据库
        piece_data = await self.piece_data_generator(game_room)
        await self.db_manager.batch_insert_game_data(session_code, piece_data)
        self.logger.info(f"棋子数据已插入数据库: {piece_data}")

        # 获取占领总部的棋子ID,后续排除掉被抽走的，剩下的需要先被移动到空位上
        for i, j in GameConfig.get_area_list(6):
            piece_id = await self.db_manager.select_one(f'{session_code}_game_data', ['piece_id'], {'x': i, 'y': j})
            game.headquarters_pieces.append(piece_id['data']['piece_id'])

        # 发送游戏开始消息
        message = self.format_data('game_event_response', {'type': 'init_game', 'game_room': game_room, 'members': members}, '', m_room_name=broadcast_room)
        await self.response_queue.put(message)
        piece_info = {i: [j['owner'], j['color'], j['rank'], (j['x'], j['y']), j['status']] for i, j in piece_data.items()}
        return self.format_data('game_event_response', {'type': 'init_pieces', 'piece_data': piece_info}, '', m_room_name=game_room)
    

    async def handle_pick_pieces(self, data_list: list):
        """处理选择棋子"""
        piece_id = data_list[1].get('piece_id')
        owner = data_list[1].get('owner')
        position = tuple(data_list[1].get('position'))
        rank = data_list[1].get('rank')
        username = data_list[4]
        game_room = data_list[5]
        game = self.room_to_game.get(game_room)
        picked_pieces_pos = [[(377, 580), (513, 580)], [(568, 525), (568, 389)],
                            [(513, 334), (377, 334)], [(322, 389), (322, 525)]]
        new_pos = None

        if game:
            index = game.members.index(username)
            async with game.picked_pieces_lock:
                if piece_id not in game.picked_pieces:
                    if game.picked_pieces[index * 2] and game.picked_pieces[index * 2 + 1]:
                        self.logger.info(f"用户 {username} 已经选择两个棋子")
                        return
                    elif not game.picked_pieces[index * 2]:
                        game.picked_pieces[index * 2] = piece_id
                        new_pos = picked_pieces_pos[index][0]
                    elif not game.picked_pieces[index * 2 + 1]:
                        game.picked_pieces[index * 2 + 1] = piece_id
                        new_pos = picked_pieces_pos[index][1]

                    if piece_id in game.headquarters_pieces:
                        game.headquarters_pieces.remove(piece_id)

                    if not game.lost_pieces_rank[owner * 2]:
                        game.lost_pieces_rank[owner * 2] = rank
                    elif not game.lost_pieces_rank[owner * 2 + 1]:
                            game.lost_pieces_rank[owner * 2 + 1] = rank

                    if position not in GameConfig.get_area_list(6):
                        if not game.lost_pieces_pos[owner * 2]:
                            game.lost_pieces_pos[owner * 2] = position
                        elif not game.lost_pieces_pos[owner * 2 + 1]:
                            game.lost_pieces_pos[owner * 2 + 1] = position
                    game.picked_count += 1

                    pick_message = self.format_data('game_event_response', {'type': 'pick_pieces', 'piece_id': piece_id, 'position': new_pos, 'pick_seat': index}, '', m_room_name=game_room)
                    await self.response_queue.put(pick_message)
                    if game.picked_count == 8:
                        await self.handle_start_game(game)

    
    async def handle_start_game(self, game):
        """处理游戏开始"""
        print(
            '被选棋子', game.picked_pieces,
            '被抽棋子位置', game.lost_pieces_pos,
            '被抽棋子等级', game.lost_pieces_rank,
            '总部剩余棋子', game.headquarters_pieces
        )
        self.logger.info(f"游戏开始: {game.room}")
        temp_lost_pos = []
        for i in game.lost_pieces_pos:
            if i:
                temp_lost_pos.append(i)
        rejust_pos = []
        for i, j in zip(game.headquarters_pieces, temp_lost_pos):
            rejust_pos.append([i, j])
        for i, j in zip(game.picked_pieces, GameConfig.get_area_list(6)):
            rejust_pos.append([i, j])
        message = self.format_data('game_event_response', {'type': 'start_game', 'rejust_pos': rejust_pos}, '', m_room_name=game.room)
        print('游戏开始', rejust_pos)
        await self.response_queue.put(message)


    async def handle_game_action(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理游戏动作"""
        # TODO: 实现游戏动作处理逻辑
        return {
            'type': 'game_action_response',
            'data': {
                'success': False,
                'error': '功能暂未实现'
            },
            'socket_id': message.get('socket_id')
        }
    
    async def handle_end_game(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理结束游戏"""
        # TODO: 实现游戏结束逻辑
        return {
            'type': 'end_game_response',
            'data': {
                'success': False,
                'error': '功能暂未实现'
            },
            'socket_id': message.get('socket_id')
        }


    # ==================== 辅助方法 ====================

    async def piece_data_generator(self, room):  # 棋子数据生成器
        self.gen_dead0 = (i for i in GameConfig.get_dead_area_list(0))
        self.gen_dead1 = (i for i in GameConfig.get_dead_area_list(1))
        self.gen_dead2 = (i for i in GameConfig.get_dead_area_list(2))
        self.gen_dead3 = (i for i in GameConfig.get_dead_area_list(3))
        num = [3, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 2]
        color = ['black', 'red', 'yellow', 'blue', 'green']
        piece_dict = {}
        random.shuffle(color)
        for code, color in zip(range(4), color):  # 按4大区域依次进行生成
            area = list(set(GameConfig.get_area_list(code)) - set(GameConfig.get_area_list(5)))
            random.shuffle(area)
            piece_id_list = []
            for i, j in zip(GameConfig.get_rank_list(), num):  # 生成字典的键，'rd020'前两位代表颜色，中间两位代表rank，最后一位是rank复数位
                for count in range(j):
                    piece_id = color[0] + color[-1] + i + str(count)
                    piece_id_list.append(piece_id)
            for i, j in zip(piece_id_list, area):  # 依次生成玩家棋子字典
                piece_dict[i] = {
                    'owner':code,
                    'color':color,
                    'rank':i[2]+i[3],
                    'x': j[0],
                    'y': j[1],
                    'status': 1
                }
        blank_piece_dict = await self.room_to_game[room].blank_generator(0)  # 生成初始空白棋子
        if blank_piece_dict:  # 确保不是None
            piece_dict.update(blank_piece_dict)  # 合并两者
        return piece_dict  # 返回完整的棋子数据列表


class Game:
    def __init__(self, session_code: str, room_name: str, members: list, db_manager: DatabaseManager, logger):
        """初始化游戏
        
        Args:
            session_code: 会话ID
            room_name: 房间名
            members: 游戏成员列表
            db_manager: 数据库管理器
            logger: 日志记录器
        """
        self.session_code = session_code
        self.room = room_name
        self.members = members
        self.db_manager = db_manager
        self.logger = logger
        self.picked_count = 0
        self.picked_pieces = ['' for _ in range(8)] # 按位置顺序记录已选择的棋子
        self.picked_pieces_lock = Lock()
        self.lost_pieces_pos = [None for _ in range(8)] # 按位置顺序记录被抽走的棋子
        self.headquarters_pieces = []
        self.lost_pieces_rank = [0 for _ in range(8)] # 按位置顺序记录被抽走的棋子的rank，后续用来计算权重以决定先行权


    async def blank_generator(self, switch, pos=None):
        '''空白棋子生成器
        Args:
            switch: 0-生成全部初始空白棋子 1-生成一个空白棋子
            room: 房间名
            pos: 生成空白棋子的位置
        '''
        blank_pos = GameConfig.get_area_list(4) + GameConfig.get_area_list(5)  # 初始空白棋子的位置
        blank_dict = {}

        def create_generator():
            counter = 0
            while True:
                yield f'bnk_{str(counter).rjust(3, "0")}'
                counter += 1
        blank_id_gen = create_generator()

        if switch == 0:
            for pos in blank_pos:  # 生成初始空白棋子字典
                blank_dict[next(blank_id_gen)] = {
                    'owner': None,
                    'color': None,
                    'rank': None,
                    'x': pos[0],
                    'y': pos[1],
                    'status': None
                }
            return blank_dict

        elif switch == 1:  # 用pos值新建一个空白棋子
            piece_id = next(blank_id_gen)
            # 创建新的空白棋子数据
            new_blank_piece = {piece_id:{
                'owner': None,
                'color': None,
                'rank': None,
                'x': pos[0] if pos else 0,
                'y': pos[1] if pos else 0,
                'status': None
            }}

            self.logger.info(f"为房间 {self.room} 在位置 {pos} 生成新空白棋子: {piece_id}")
            return new_blank_piece