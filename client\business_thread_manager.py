"""
业务线程管理器模块
包含BusinessThreadedManager类，用于管理业务线程的异步事件循环
"""

import threading
import asyncio
from typing import Optional
from business_message_hub import BusinessMessageHub
from business_network import BusinessNetwork


class BusinessThreadedManager:
    """线程化事件循环管理器
    
    用于在独立线程中运行异步事件循环，提供线程安全的任务提交接口。
    适用于需要在多线程环境中管理异步任务的场景。
    """
    
    def __init__(self, name: str, logger, business_signals, ui_signals):
        """初始化线程事件循环管理器
        
        Args:
            name: 线程名称，用于日志标识
            logger: 日志记录器实例
            business_signals: 业务信号对象
            ui_signals: UI信号对象
        """
        self.name = name
        self.logger = logger
        self.business_signals = business_signals
        self.ui_signals = ui_signals
        self.business_message_hub = None
        self.business_network = None
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self._thread: Optional[threading.Thread] = None
        self._running = False
        self._loop_ready = threading.Event()  # 添加事件循环就绪标志
        self.logger.info(f"创建线程事件循环管理器: {name}")

        """启动线程和事件循环"""
        if self._running:
            self.logger.warning(f"线程 {self.name} 已在运行，跳过启动")
            return

        self.logger.info(f"正在启动线程: {self.name}")
        self._running = True
        self._thread = threading.Thread(target=self._run_event_loop, name=self.name)
        self._thread.daemon = True
        self._thread.start()

        # 等待事件循环就绪
        self.logger.debug(f"等待线程 {self.name} 事件循环就绪...")
        if not self._loop_ready.wait(timeout=5.0):
            self._running = False
            raise RuntimeError(f"线程 {self.name} 事件循环启动超时")

        self.logger.info(f"线程 {self.name} 启动成功")

    def _run_event_loop(self):
        """在线程中运行事件循环"""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)

            # 创建业务消息处理中心
            self.business_message_hub = BusinessMessageHub(self.loop, self.logger, self.business_signals, self.ui_signals)

            # 创建业务网络通信模块
            self.business_network = BusinessNetwork(self.logger, self.business_message_hub)

            # 设置business_message_hub的网络模块引用
            self.business_message_hub.set_business_network(self.business_network)

            # 通知主线程事件循环已就绪
            self._loop_ready.set()

            self.logger.info(f"线程 {self.name} 的事件循环已启动")

            # 启动网络通信模块（在事件循环中）
            if self.business_network:
                self.loop.create_task(self.business_network.start())
                self.logger.info("业务网络通信模块启动任务已创建")

            self.loop.run_forever()
            self.logger.info(f"线程 {self.name} 的事件循环已停止，开始清理")
        except Exception as e:
            self.logger.error(f"线程 {self.name} 事件循环运行异常: {e}")
            # 即使异常也要通知主线程，避免无限等待
            self._loop_ready.set()
        finally:
            # 统一在这里处理所有清理工作
            if self.loop:
                pending = asyncio.all_tasks(self.loop)
                if pending:
                    self.logger.info(f"线程 {self.name} 正在取消 {len(pending)} 个未完成的任务")
                    for task in pending:
                        task.cancel()
                    # 等待所有任务完成取消
                    try:
                        self.loop.run_until_complete(
                            asyncio.gather(*pending, return_exceptions=True)
                        )
                        self.logger.info(f"线程 {self.name} 所有任务已取消完成")
                    except Exception as e:
                        self.logger.error(f"线程 {self.name} 取消任务时出错: {e}")
                else:
                    self.logger.info(f"线程 {self.name} 没有待清理的任务")

                self.loop.close()
                self.logger.info(f"线程 {self.name} 事件循环已关闭")
                self.loop = None

    def stop_business_thread(self):
        """停止事件循环和线程"""
        if not self._running:
            self.logger.warning(f"线程 {self.name} 已停止，跳过停止操作")
            return

        self.logger.info(f"正在停止线程: {self.name}")
        self._running = False
        if self.loop is not None:
            # 确保在正确的线程中停止事件循环
            self.logger.debug(f"向线程 {self.name} 发送停止信号")
            self.loop.call_soon_threadsafe(self._stop_loop)
        if self._thread is not None:
            self.logger.debug(f"等待线程 {self.name} 结束")
            self._thread.join(timeout=10.0)  # 添加超时避免无限等待
            if self._thread.is_alive():
                self.logger.error(f"线程 {self.name} 停止超时")
            else:
                self.logger.info(f"线程 {self.name} 已成功停止")

    def stop(self):
        """停止业务线程 - 为main.py提供的接口方法"""
        self.logger.info("正在停止业务线程...")

        # 首先停止BusinessNetwork
        if self.business_network is not None:
            if self.loop is not None:
                # 在事件循环中停止网络模块
                future = asyncio.run_coroutine_threadsafe(self.business_network.stop(), self.loop)
                try:
                    future.result(timeout=5.0)  # 等待网络模块停止
                    self.logger.info("BusinessNetwork 已停止")
                except Exception as e:
                    self.logger.error(f"停止BusinessNetwork时出错: {e}")

        # 然后停止BusinessMessageHub
        if self.business_message_hub is not None:
            self.business_message_hub.is_running = False
            self.logger.info("BusinessMessageHub 停止信号已发送")

        # 最后停止事件循环和线程
        self.stop_business_thread()
        self.logger.info("业务线程已完全停止")

    def _stop_loop(self):
        """在事件循环线程中停止循环"""
        if self.loop is not None:
            self.logger.debug(f"线程 {self.name} 正在停止事件循环")
            # 只停止循环，不取消任务
            self.loop.stop()
        else:
            self.logger.warning(f"线程 {self.name} 的事件循环已为空，无需停止")
