#!/usr/bin/env python3
"""
密码预验证器在项目中的使用示例
演示如何在admin_console和security_validator中集成密码预验证器
"""

import asyncio
from typing import Dict, Any
from tool_kit import PasswordPreValidator


class EnhancedSecurityValidator:
    """增强的安全验证器，集成密码预验证功能"""
    
    def __init__(self):
        """初始化验证器"""
        self.password_validator = PasswordPreValidator()
        # 设置管理员密码的强度要求为强级别
        self.password_validator.set_strength_level(PasswordPreValidator.STRENGTH_STRONG)
    
    async def verify_security_key(self, key: str) -> bool:
        """
        异步验证安全密钥（增强版本）
        
        Args:
            key (str): 待验证的密钥
            
        Returns:
            bool: 验证成功返回True，失败返回False
        """
        # 模拟异步操作
        await asyncio.sleep(0.01)
        
        # 首先进行格式预验证
        validation_result = await self.password_validator.validate_password(key)
        
        if not validation_result['is_valid']:
            print(f"密码格式验证失败: {', '.join(validation_result['errors'])}")
            return False
        
        # 格式验证通过后，进行实际的密码匹配
        if key == "qwert12345":
            return True
        else:
            return False
    
    async def create_new_password(self, password: str) -> Dict[str, Any]:
        """
        创建新密码时的验证
        
        Args:
            password: 新密码
            
        Returns:
            Dict: 验证结果和建议
        """
        # 使用强级别验证新密码
        result = await self.password_validator.validate_password(
            password, 
            PasswordPreValidator.STRENGTH_STRONG
        )
        
        return {
            'is_valid': result['is_valid'],
            'score': result['score'],
            'errors': result['errors'],
            'suggestions': result['suggestions'],
            'can_create': result['is_valid'] and result['score'] >= 70  # 要求评分70以上
        }


class EnhancedAdminConsole:
    """增强的管理员控制台，集成密码预验证功能"""
    
    def __init__(self, logger, pending_queue: asyncio.Queue):
        """初始化管理员控制台"""
        self.logger = logger
        self.pending_queue = pending_queue
        self.is_authenticated = False
        self.response_queue = asyncio.Queue()
        self.security_validator = EnhancedSecurityValidator()
        self.password_validator = PasswordPreValidator()
        self.logger.info("增强管理员控制台初始化完成")
    
    async def validate_input_password(self, password: str) -> Dict[str, Any]:
        """
        验证输入的密码格式
        
        Args:
            password: 输入的密码
            
        Returns:
            Dict: 验证结果
        """
        # 使用中等强度验证输入密码
        result = await self.password_validator.validate_password(
            password, 
            PasswordPreValidator.STRENGTH_MEDIUM
        )
        
        return result
    
    async def process_authentication(self) -> str:
        """处理管理员认证（增强版本）"""
        self.logger.info("=== 管理员认证阶段（增强版本） ===")
        print("\n=== 4inWar 服务器管理员认证（增强版本） ===")
        
        await asyncio.sleep(0.1)
        
        try:
            password = await self.input_password()
            
            # 首先进行格式预验证
            validation_result = await self.validate_input_password(password)
            
            if not validation_result['is_valid']:
                print("✗ 密码格式不符合要求:")
                for error in validation_result['errors']:
                    print(f"  - {error}")
                
                if validation_result['suggestions']:
                    print("建议:")
                    for suggestion in validation_result['suggestions']:
                        print(f"  - {suggestion}")
                
                return "retry"
            
            # 格式验证通过后，进行实际认证
            if await self.security_validator.verify_security_key(password):
                self.logger.info("管理员密码正确")
                self.is_authenticated = True
                print("✓ 管理员认证成功！准备启动网络模块...")
                return "success"
            else:
                print("✗ 登录失败，请重试...")
                return "retry"
                
        except Exception as e:
            self.logger.error(f"认证过程异常: {e}")
            return "retry"
    
    async def input_password(self) -> str:
        """等待真实用户输入（模拟版本）"""
        # 在实际应用中，这里会是真正的用户输入
        # 为了演示，我们返回一个测试密码
        await asyncio.sleep(0.1)
        return "qwert12345"  # 模拟用户输入
    
    async def change_password_workflow(self, old_password: str, new_password: str) -> bool:
        """
        密码修改工作流程
        
        Args:
            old_password: 旧密码
            new_password: 新密码
            
        Returns:
            bool: 修改是否成功
        """
        print("\n=== 密码修改流程 ===")
        
        # 验证旧密码
        if not await self.security_validator.verify_security_key(old_password):
            print("✗ 旧密码验证失败")
            return False
        
        # 验证新密码格式
        result = await self.security_validator.create_new_password(new_password)
        
        if not result['can_create']:
            print("✗ 新密码不符合要求:")
            for error in result['errors']:
                print(f"  - {error}")
            
            if result['suggestions']:
                print("建议:")
                for suggestion in result['suggestions']:
                    print(f"  - {suggestion}")
            
            return False
        
        print(f"✓ 新密码验证通过 (强度评分: {result['score']}/100)")
        print("✓ 密码修改成功")
        return True


async def demo_usage():
    """演示使用方法"""
    print("=== 密码预验证器集成演示 ===\n")
    
    # 创建增强的安全验证器
    security_validator = EnhancedSecurityValidator()
    
    # 测试密码验证
    test_passwords = [
        "123",              # 太短
        "password",         # 常见弱密码
        "qwert12345",      # 符合要求的密码
        "MyStrongP@ss123", # 强密码
    ]
    
    print("1. 测试安全密钥验证:")
    print("-" * 40)
    
    for password in test_passwords:
        print(f"\n测试密码: '{password}'")
        is_valid = await security_validator.verify_security_key(password)
        status = "✓ 通过" if is_valid else "✗ 失败"
        print(f"验证结果: {status}")
    
    print("\n\n2. 测试新密码创建:")
    print("-" * 40)
    
    new_passwords = [
        "weak123",           # 弱密码
        "StrongPassword123", # 强密码
        "VeryStr0ng!P@ss",  # 很强密码
    ]
    
    for password in new_passwords:
        print(f"\n新密码: '{password}'")
        result = await security_validator.create_new_password(password)
        status = "✓ 可创建" if result['can_create'] else "✗ 不可创建"
        print(f"创建结果: {status} (评分: {result['score']}/100)")
        
        if result['errors']:
            print(f"错误: {', '.join(result['errors'])}")
    
    print("\n\n3. 测试管理员控制台集成:")
    print("-" * 40)
    
    # 模拟logger和队列
    class MockLogger:
        def info(self, msg): print(f"[INFO] {msg}")
        def error(self, msg): print(f"[ERROR] {msg}")
    
    logger = MockLogger()
    queue = asyncio.Queue()
    
    # 创建增强的管理员控制台
    admin_console = EnhancedAdminConsole(logger, queue)
    
    # 测试认证流程
    auth_result = await admin_console.process_authentication()
    print(f"认证结果: {auth_result}")
    
    # 测试密码修改流程
    if auth_result == "success":
        change_result = await admin_console.change_password_workflow(
            "qwert12345",      # 旧密码
            "NewStr0ng!P@ss"   # 新密码
        )
        print(f"密码修改结果: {'成功' if change_result else '失败'}")


if __name__ == "__main__":
    asyncio.run(demo_usage())
