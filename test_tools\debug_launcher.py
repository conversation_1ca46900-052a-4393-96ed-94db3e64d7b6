#!/usr/bin/env python3
"""
4inWar 多人游戏调试启动器
用于同时启动服务器和多个客户端进行调试
"""

import subprocess
import time
import os
import sys
import signal
from typing import List, Optional

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_tools.environment_detector import get_environment_config

class GameDebugLauncher:
    def __init__(self):
        # 使用环境检测工具自动获取配置
        env_config = get_environment_config()
        self.python_path = env_config['python_path']
        self.project_path = env_config['project_path']
        self.server_script = "server/main.py"
        self.client_script = "client/main.py"

        self.server_process: Optional[subprocess.Popen] = None
        self.client_processes: List[subprocess.Popen] = []

        # 测试用户配置
        self.test_users = [
            {'name': '施恩铭', 'password': 'q1'},
            {'name': '陈琪', 'password': 'q1'},
            {'name': '杨燕超', 'password': 'q1'},
            {'name': '王锋玮', 'password': 'q1'},
        ]


        
    def check_environment(self) -> bool:
        """检查环境配置"""
        print("🔍 检查环境配置...")
        
        if not os.path.exists(self.python_path):
            print(f"❌ Python路径不存在: {self.python_path}")
            return False
            
        if not os.path.exists(self.project_path):
            print(f"❌ 项目路径不存在: {self.project_path}")
            return False
            
        server_full_path = os.path.join(self.project_path, self.server_script)
        client_full_path = os.path.join(self.project_path, self.client_script)
        
        if not os.path.exists(server_full_path):
            print(f"❌ 服务器脚本不存在: {server_full_path}")
            return False
            
        if not os.path.exists(client_full_path):
            print(f"❌ 客户端脚本不存在: {client_full_path}")
            return False
            
        print("✅ 环境配置检查通过")
        return True
    
    def start_server(self) -> bool:
        """启动服务器"""
        print("🚀 启动服务器...")
        try:
            self.server_process = subprocess.Popen(
                [self.python_path, self.server_script],
                cwd=self.project_path,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )
            print(f"✅ 服务器已启动 (PID: {self.server_process.pid})")
            return True
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
            return False
    
    def start_clients(self, count: int = 4) -> bool:
        """启动多个客户端"""
        print(f"🎮 启动 {count} 个客户端...")

        # 确保不超过可用用户数
        actual_count = min(count, len(self.test_users))
        if actual_count < count:
            print(f"⚠️ 只有 {len(self.test_users)} 个测试用户，将启动 {actual_count} 个客户端")

        for i in range(actual_count):
            try:
                # 为每个客户端设置不同的环境变量
                env = os.environ.copy()
                user_info = self.test_users[i]
                env['CLIENT_ID'] = str(i + 1)
                env['CLIENT_NAME'] = user_info['name']
                env['CLIENT_PASSWORD'] = user_info['password']

                client_process = subprocess.Popen(
                    [self.python_path, self.client_script],
                    cwd=self.project_path,
                    env=env,
                    creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
                )
                self.client_processes.append(client_process)
                print(f"✅ 客户端 {i+1} 已启动 (PID: {client_process.pid}) - 用户: {user_info['name']}")

                # 客户端之间稍微延迟，避免同时连接
                if i < actual_count - 1:
                    time.sleep(2)  # 增加延迟时间

            except Exception as e:
                print(f"❌ 客户端 {i+1} 启动失败: {e}")
                return False

        return True
    
    def check_processes(self):
        """检查进程状态"""
        print("\n📊 进程状态检查:")
        
        # 检查服务器
        if self.server_process:
            if self.server_process.poll() is None:
                print(f"   ✅ 服务器运行中 (PID: {self.server_process.pid})")
            else:
                print(f"   ❌ 服务器已停止 (退出码: {self.server_process.returncode})")
        else:
            print("   ❌ 服务器未启动")
        
        # 检查客户端
        for i, client in enumerate(self.client_processes):
            if client.poll() is None:
                print(f"   ✅ 客户端 {i+1} 运行中 (PID: {client.pid})")
            else:
                print(f"   ❌ 客户端 {i+1} 已停止 (退出码: {client.returncode})")
    
    def stop_all(self):
        """停止所有进程"""
        print("\n🛑 正在停止所有进程...")
        
        # 停止客户端
        for i, client in enumerate(self.client_processes):
            if client.poll() is None:
                try:
                    client.terminate()
                    client.wait(timeout=5)
                    print(f"✅ 客户端 {i+1} 已停止")
                except subprocess.TimeoutExpired:
                    client.kill()
                    print(f"⚠️ 客户端 {i+1} 被强制终止")
                except Exception as e:
                    print(f"❌ 停止客户端 {i+1} 失败: {e}")
        
        # 停止服务器
        if self.server_process and self.server_process.poll() is None:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                print("✅ 服务器已停止")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("⚠️ 服务器被强制终止")
            except Exception as e:
                print(f"❌ 停止服务器失败: {e}")
    
    def register_test_users(self):
        """注册测试用户"""
        print("🔧 检查并注册测试用户...")
        try:
            register_script = os.path.join(os.path.dirname(__file__), "register_test_users.py")
            if os.path.exists(register_script):
                result = subprocess.run(
                    [self.python_path, register_script],
                    cwd=self.project_path,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                if result.returncode == 0:
                    print("✅ 测试用户注册完成")
                    return True
                else:
                    print(f"⚠️ 用户注册脚本执行失败: {result.stderr}")
                    print("继续启动，假设用户已存在...")
                    return True
            else:
                print("⚠️ 用户注册脚本不存在，假设用户已注册")
                return True
        except Exception as e:
            print(f"⚠️ 注册用户时出错: {e}")
            print("继续启动，假设用户已存在...")
            return True

    def run(self):
        """运行调试环境"""
        print("=" * 50)
        print("🎮 4inWar 多人游戏调试环境")
        print("=" * 50)
        print("📋 测试用户配置:")
        for i, user in enumerate(self.test_users, 1):
            print(f"   {i}. {user['name']} (密码: {user['password']})")
        print("=" * 50)

        # 检查环境
        if not self.check_environment():
            return

        # 注册测试用户
        self.register_test_users()
        
        try:
            # 启动服务器
            if not self.start_server():
                return
            
            # 等待服务器启动
            print("⏳ 等待服务器启动...")
            time.sleep(3)
            
            # 启动客户端
            if not self.start_clients(4):
                self.stop_all()
                return
            
            print("\n" + "=" * 50)
            print("✅ 所有进程已启动！")
            print("=" * 50)
            
            # 交互式管理
            self.interactive_management()
            
        except KeyboardInterrupt:
            print("\n⚠️ 收到中断信号")
        finally:
            self.stop_all()
    
    def interactive_management(self):
        """交互式管理界面"""
        print("\n🎛️ 管理选项:")
        print("   's' - 查看进程状态")
        print("   'r' - 重启服务器")
        print("   'q' - 退出并停止所有进程")
        print("   'h' - 显示帮助")
        
        while True:
            try:
                choice = input("\n请选择操作: ").strip().lower()
                
                if choice == 's':
                    self.check_processes()
                elif choice == 'r':
                    self.restart_server()
                elif choice == 'q':
                    break
                elif choice == 'h':
                    print("\n🎛️ 管理选项:")
                    print("   's' - 查看进程状态")
                    print("   'r' - 重启服务器")
                    print("   'q' - 退出并停止所有进程")
                    print("   'h' - 显示帮助")
                else:
                    print("❌ 无效选项，请重新选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def restart_server(self):
        """重启服务器"""
        print("🔄 重启服务器...")
        
        # 停止当前服务器
        if self.server_process and self.server_process.poll() is None:
            self.server_process.terminate()
            self.server_process.wait(timeout=5)
        
        # 启动新服务器
        time.sleep(1)
        if self.start_server():
            print("✅ 服务器重启成功")
        else:
            print("❌ 服务器重启失败")

def main():
    launcher = GameDebugLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
