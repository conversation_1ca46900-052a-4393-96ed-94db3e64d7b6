from email import message
from aiohttp import web
import aiohttp
import asyncio
import json
from typing import Dict, Any, List, Set, Optional


class ConnectionManager:
    def __init__(self, logger, network_receive_queue, network_send_queue):
        self.logger = logger
        self.active_connections: Dict[str, List] = {}  # {'socket_id': [username, websocket]}
        self.active_connections_lock = asyncio.Lock()
        self.connection_last_seen: Dict[str, float] = {}  # 记录连接最后活跃时间
        self.connection_last_heartbeat: Dict[str, float] = {}  # 记录连接最后心跳时间
        self.heartbeat_interval = 30  # 心跳间隔（秒）
        self.connection_timeout = 90  # 连接超时时间（秒）- 增加到90秒避免误清理
        self.heartbeat_timeout = 60  # 心跳超时时间（秒）
        self.room_members: Dict[str, Dict[str, Any]] = {
            '激战房间': {'is_playing': 1, '张三': None, '李四': None, '王五': None, '赵六': None},
            '高手对决': {'is_playing': 1, '小明': None, '小红': None, '小刚': None, '小美': None},
            '新手村': {'is_playing': 0, '菜鸟一号': None, '菜鸟二号': None, '菜鸟三号': None, '菜鸟四号': None},
            '休闲娱乐': {'is_playing': 0, '玩家甲': None, '玩家乙': None, '玩家丙': None, '玩家丁': None},
            '空房间': {'is_playing': 0, '用户A': None, '用户B': None, '用户C': None, '用户D': None},
            '满员等待': {'is_playing': 0, '用户A': None, '用户B': None, '用户C': None, '用户D': None}
        }
        self.room_members_lock = asyncio.Lock()

        # 可序列化的房间信息字典（不包含连接对象）
        self.room_members_for_sync: Dict[str, Dict[str, Any]] = {
            # 正在游戏的房间 - 必须有4个用户
            '激战房间': {
                'is_playing': 1,
                'members': ['张三', '李四', '王五', '赵六'],
                'member_count': 4
            },
            '高手对决': {
                'is_playing': 1,
                'members': ['小明', '小红', '小刚', '小美'],
                'member_count': 4
            },

            # 等待中的房间 - 与room_members保持一致
            '新手村': {
                'is_playing': 0,
                'members': ['菜鸟一号', '菜鸟二号', '菜鸟三号', '菜鸟四号'],
                'member_count': 4
            },
            '休闲娱乐': {
                'is_playing': 0,
                'members': ['玩家甲', '玩家乙', '玩家丙', '玩家丁'],
                'member_count': 4
            },
            '空房间': {
                'is_playing': 0,
                'members': ['用户A', '用户B', '用户C', '用户D'],
                'member_count': 4
            },
            '满员等待': {
                'is_playing': 0,
                'members': ['用户A', '用户B', '用户C', '用户D'],
                'member_count': 4
            }
        }

        # 房间锁定座位字典 - 维护4个座位的用户及锁定状态 [[username, locked], ...]
        # 座位编号：0=下，1=右，2=上，3=左（游戏轮流方向）
        self.room_locked_seats_lock = asyncio.Lock()
        self.room_locked_seats: Dict[str, List] = {
            # 正在游戏的房间 - 4个座位全部锁定
            '激战房间': [['张三', 2], ['李四', 2], ['王五', 2], ['赵六', 2]],  # 全部座位锁定
            '高手对决': [['小明', 2], ['小红', 2], ['小刚', 2], ['小美', 2]],  # 全部座位锁定

            # 等待中的房间 - 部分座位锁定
            '新手村': [['菜鸟一号', 1], ['', 0], ['菜鸟二号', 0], ['', 0]],  # 0号座位锁定, 2号座位有人占座但未锁定
            '休闲娱乐': [['', 0], ['玩家甲', 1], ['', 0], ['', 0]],           # 1号座位锁定
            '空房间': [['', 0],['', 0],['', 0],['', 0]],                   # 无人占座且无座位锁定
            '满员等待': [['用户A', 1], ['用户B', 1], ['', 0], ['用户D', 1]]    # 0,1号座位锁定，3号座位有人占座但未锁定
        }
        self.network_receive_queue = network_receive_queue  # 网络接收队列
        self.network_send_queue = network_send_queue  # 网络发送队列
        self.is_shutting_down = False


    async def handle_new_websocket(self, request):
        """处理新的WebSocket连接请求"""
        if self.is_shutting_down:
            self.logger.info("服务器正在关闭")
            return web.Response(status=503, text="服务器正在关闭")
            
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        socket_id = await self.connect(ws)
        
        try:
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    await self.handle_receive_message(msg.data, socket_id, ws)
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    self.logger.error(f"WebSocket连接错误 (ID: {socket_id}): {ws.exception()}")
        finally:
            self.logger.info(f"{socket_id})的WebSocket连接关闭")
            await self.disconnect(socket_id)
        
        return ws

    async def connect(self, websocket: web.WebSocketResponse) -> str:
        """添加新的WebSocket连接并返回连接ID（使用对象地址）"""
        socket_id = str(id(websocket))  # 使用WebSocket对象的内存地址
        import time
        async with self.active_connections_lock:
            self.active_connections[socket_id] = ['', websocket]  # [username, websocket]
            current_time = time.time()
            self.connection_last_seen[socket_id] = current_time  # 记录连接时间
            self.connection_last_heartbeat[socket_id] = current_time  # 记录心跳时间
            self.logger.info(f"新的连接加入 (地址: {socket_id})，当前活动连接数：{len(self.active_connections)}")
        return socket_id
    
    async def disconnect(self, socket_id: str):
        """移除WebSocket连接"""
        username = None
        async with self.active_connections_lock:
            if socket_id in self.active_connections:
                username, websocket = self.active_connections[socket_id]
                if not websocket.closed:
                    await websocket.close()
                del self.active_connections[socket_id]
            # 清理心跳记录
            if socket_id in self.connection_last_seen:
                del self.connection_last_seen[socket_id]
            if socket_id in self.connection_last_heartbeat:
                del self.connection_last_heartbeat[socket_id]
            self.logger.info(f"连接断开 (ID: {socket_id}, 用户: {username or '未登录'})，当前活动连接数：{len(self.active_connections)}")

        # 如果用户已登录，清理房间信息
        if username:
            await self.cleanup_user_from_rooms(username)

    async def cleanup_dead_connections(self):
        """清理僵尸连接和超时连接"""
        import time
        current_time = time.time()
        dead_connections = []

        async with self.active_connections_lock:
            for socket_id, connection_data in list(self.active_connections.items()):
                try:
                    username, ws = connection_data
                    # 检查连接是否已关闭
                    if ws.closed:
                        dead_connections.append(socket_id)
                        self.logger.info(f"发现已关闭的连接: {socket_id} (用户: {username or '未登录'})")
                        continue

                    # 检查心跳是否超时（优先使用心跳时间）
                    last_heartbeat = self.connection_last_heartbeat.get(socket_id, current_time)
                    last_seen = self.connection_last_seen.get(socket_id, current_time)

                    # 使用心跳时间和活动时间中较新的一个
                    last_active = max(last_heartbeat, last_seen)

                    if current_time - last_heartbeat > self.heartbeat_timeout:
                        dead_connections.append(socket_id)
                        self.logger.info(f"心跳超时: {socket_id} (心跳超时 {current_time - last_heartbeat:.1f} 秒)")
                        continue
                    elif current_time - last_active > self.connection_timeout:
                        dead_connections.append(socket_id)
                        self.logger.info(f"连接超时: {socket_id} (活动超时 {current_time - last_active:.1f} 秒)")
                        continue

                except Exception as e:
                    self.logger.error(f"检查连接状态时出错 (ID: {socket_id}): {e}")
                    dead_connections.append(socket_id)

            # 清理死连接
            cleaned_usernames = []
            for socket_id in dead_connections:
                username = None
                if socket_id in self.active_connections:
                    try:
                        username, websocket = self.active_connections[socket_id]
                        if not websocket.closed:
                            await websocket.close()
                        if username:
                            cleaned_usernames.append(username)
                    except Exception as e:
                        self.logger.error(f"关闭死连接时出错 (ID: {socket_id}): {e}")
                    del self.active_connections[socket_id]

                if socket_id in self.connection_last_seen:
                    del self.connection_last_seen[socket_id]

            # 清理房间信息
            for username in cleaned_usernames:
                await self.cleanup_user_from_rooms(username)

        if dead_connections:
            self.logger.info(f"清理了 {len(dead_connections)} 个僵尸连接，当前活动连接数：{len(self.active_connections)}")

        return len(dead_connections)

    async def _connection_cleanup_loop(self):
        """定期清理僵尸连接和发送心跳的后台任务"""
        heartbeat_counter = 0
        while not self.is_shutting_down:
            try:
                await asyncio.sleep(10)  # 每10秒检查一次
                heartbeat_counter += 1

                # 每30秒发送一次心跳包
                if heartbeat_counter % 3 == 0:
                    await self._send_heartbeat_to_all()

                # 每60秒清理一次僵尸连接
                if heartbeat_counter % 6 == 0:
                    await self.cleanup_dead_connections()
                    heartbeat_counter = 0  # 重置计数器

            except asyncio.CancelledError:
                self.logger.info("连接清理任务被取消")
                break
            except Exception as e:
                self.logger.error(f"连接清理任务出错: {e}")
                await asyncio.sleep(5)  # 出错后等待5秒再继续

    async def _send_heartbeat_to_all(self):
        """向所有活动连接发送心跳包"""
        if not self.active_connections:
            return

        success_count = 0
        total_count = len(self.active_connections)

        # 获取连接列表的副本以避免在迭代时修改
        async with self.active_connections_lock:
            connection_ids = list(self.active_connections.keys())

        for socket_id in connection_ids:
            if await self.send_heartbeat_to_client(socket_id):
                success_count += 1

        if success_count < total_count:
            self.logger.warning(f"心跳发送: {success_count}/{total_count} 成功")
        else:
            self.logger.info(f"心跳发送: {success_count}/{total_count} 成功")

    async def force_cleanup_connections(self):
        """强制清理所有连接（供外部调用）"""
        self.logger.info("开始强制清理所有连接...")
        cleaned_count = await self.cleanup_dead_connections()
        self.logger.info(f"强制清理完成，清理了 {cleaned_count} 个连接")
        return cleaned_count

    async def get_connection_status(self):
        """获取连接状态信息"""
        import time
        current_time = time.time()
        status = {
            'total_connections': len(self.active_connections),
            'connection_details': []
        }

        async with self.active_connections_lock:
            for socket_id, connection_data in self.active_connections.items():
                username, ws = connection_data
                last_seen = self.connection_last_seen.get(socket_id, current_time)
                inactive_time = current_time - last_seen

                detail = {
                    'socket_id': socket_id,
                    'username': username or '未登录',
                    'is_closed': ws.closed,
                    'inactive_seconds': round(inactive_time, 1),
                    'is_timeout': inactive_time > self.connection_timeout
                }
                status['connection_details'].append(detail)

        return status

    async def update_connection_username(self, socket_id: str, username: str):
        """更新连接的用户名"""
        async with self.active_connections_lock:
            if socket_id in self.active_connections:
                connection_data = self.active_connections[socket_id]
                # 更新用户名，保持websocket不变
                self.active_connections[socket_id] = [username, connection_data[1]]
                self.logger.info(f"更新连接用户名: {socket_id} -> {username}")
                return True
            else:
                self.logger.warning(f"尝试更新不存在的连接用户名: {socket_id}")
                return False

    async def check_and_cleanup_duplicate_login(self, username: str, current_socket_id: str):
        """检查并清理重复登录的连接"""
        duplicate_connections = []

        async with self.active_connections_lock:
            for socket_id, connection_data in list(self.active_connections.items()):
                stored_username, websocket = connection_data
                # 找到同一用户名但不同socket_id的连接
                if stored_username == username and socket_id != current_socket_id:
                    duplicate_connections.append(socket_id)
                    self.logger.warning(f"发现重复登录连接: 用户 {username}, 旧连接 {socket_id}, 新连接 {current_socket_id}")

        # 清理重复连接
        for old_socket_id in duplicate_connections:
            await self.disconnect(old_socket_id)
            self.logger.info(f"已清理重复登录的旧连接: {old_socket_id} (用户: {username})")

    async def cleanup_user_from_rooms(self, username: str):
        """根据用户名从所有房间中清理用户信息"""
        if not username:
            return

        # 先收集用户所在的房间名（避免在迭代时修改字典）
        user_rooms = []
        async with self.room_members_lock:
            for room_name, room_data in self.room_members.items():
                if username in room_data:
                    user_rooms.append(room_name)

        # 使用已有的remove_room_member方法逐个清理
        cleaned_rooms = []
        deleted_rooms = []

        for room_name in user_rooms:
            result = await self.remove_room_member(room_name, username)
            if result is not None:  # 用户确实在房间中
                cleaned_rooms.append(room_name)
                if result == 0:  # 房间已被删除
                    deleted_rooms.append(room_name)
                    self.logger.info(f"从房间 '{room_name}' 中清理用户 {username}，房间已删除（空房间）")
                else:  # 房间仍存在
                    self.logger.info(f"从房间 '{room_name}' 中清理用户: {username}")

        if cleaned_rooms:
            self.logger.info(f"用户 {username} 已从 {len(cleaned_rooms)} 个房间中清理，其中 {len(deleted_rooms)} 个空房间已删除")

    async def _handle_heartbeat(self, socket_id: str, message: Dict[str, Any]):
        """处理客户端心跳包"""
        import time
        current_time = time.time()

        # 更新心跳时间
        if socket_id in self.connection_last_heartbeat:
            self.connection_last_heartbeat[socket_id] = current_time

        # 发送心跳响应
        heartbeat_response = {
            'type': 'heartbeat_response',
            'data': {
                'server_time': current_time,
                'status': 'alive'
            },
            'socket_id': socket_id,
            'room_name': '',
            'timestamp': current_time,
            'message_id': message.get('message_id', ''),
            'source': 'server'
        }

        try:
            if socket_id in self.active_connections:
                username, websocket = self.active_connections[socket_id]
                if websocket and not websocket.closed:
                    await websocket.send_json(heartbeat_response)
                    # self.logger.debug(f"发送心跳响应给客户端 (ID: {socket_id})")
        except Exception as e:
            self.logger.error(f"发送心跳响应失败 (ID: {socket_id}): {e}")

    async def send_heartbeat_to_client(self, socket_id: str):
        """主动向客户端发送心跳包"""
        import time
        current_time = time.time()

        heartbeat_message = {
            'type': 'heartbeat',
            'data': {
                'server_time': current_time,
                'message': 'ping'
            },
            'socket_id': socket_id,
            'room_name': '',
            'timestamp': current_time,
            'message_id': f'heartbeat_{int(current_time * 1000)}',
            'source': 'server'
        }

        try:
            if socket_id in self.active_connections:
                username, websocket = self.active_connections[socket_id]
                if websocket and not websocket.closed:
                    await websocket.send_json(heartbeat_message)
                    # self.logger.debug(f"发送心跳包给客户端 (ID: {socket_id})")
                    return True
        except Exception as e:
            self.logger.error(f"发送心跳包失败 (ID: {socket_id}): {e}")

        return False

    async def _handle_heartbeat_response(self, socket_id: str, message: Dict[str, Any]):
        """处理客户端的心跳响应"""
        import time
        current_time = time.time()

        # 更新心跳时间
        if socket_id in self.connection_last_heartbeat:
            self.connection_last_heartbeat[socket_id] = current_time
            # self.logger.debug(f"收到客户端心跳响应 (ID: {socket_id})")

    async def handle_receive_message(self, message: str, socket_id: str, ws: web.WebSocketResponse):   # 网络消息包格式规范：{'type': str, 'data': dict}
        """处理接收到的消息

        安全机制：
        - 屏蔽admin_command和admin_response消息类型
        - admin命令只能通过管理员控制台发送
        - 防止客户端绕过认证机制
        """
        # 更新连接活跃时间
        import time
        if socket_id in self.connection_last_seen:
            self.connection_last_seen[socket_id] = time.time()
        try:
            json_message: Dict[str, Any] = json.loads(message)

            # 完整的消息格式验证
            if not self._validate_complete_message_format(json_message):
                await self._send_error_response(socket_id, "消息格式错误")
                return

            # 补全七字段格式
            json_message = self._ensure_seven_fields(json_message, socket_id)

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败 (ID: {socket_id}): {e}")
            await self._send_error_response(socket_id, "无效的JSON格式")
            return
        except Exception as e:
            self.logger.error(f"消息解析异常 (ID: {socket_id}): {e}")
            await self._send_error_response(socket_id, "服务器内部错误")
            return
        
        '''网络层的初步消息路由'''
        message_type = json_message.get('type','')
        try:
            # 安全检查：屏蔽admin相关消息类型
            if message_type in ['admin_command', 'admin_response']:
                self.logger.warning(f'客户端 {socket_id} 尝试发送被禁止的admin消息类型: {message_type}')
                return

            # 处理心跳包（直接在网络层处理，不进入消息总线）
            elif message_type == 'heartbeat':
                await self._handle_heartbeat(socket_id, json_message)
                return

            # 处理心跳响应（直接在网络层处理，不进入消息总线）
            elif message_type == 'heartbeat_response':
                await self._handle_heartbeat_response(socket_id, json_message)
                return

            elif message_type == 'room_event':
                await self._handle_room_event(json_message, socket_id, ws)
                return
            
            # 处理聊天消息（直接广播，不进入消息总线）
            elif message_type == 'chat':
                await self.send_message(json_message)
                self.logger.debug(f"Chat消息已直接广播，不进入消息总线: {socket_id}")

            else:
                # 其他消息类型（如test）放入网络接收队列，进入消息总线
                if self.network_receive_queue:
                    # 消息已经过完整验证和七字段补全，直接放入队列
                    await self.network_receive_queue.put(json_message)
                    self.logger.timing("NET_RECV", socket_id, message_type)  # 性能日志
                    self.logger.info(f'📥 [Network] {message_type}消息已放入network_receive_queue (ID: {socket_id})')
                else:
                    self.logger.error(f'❌ [Network] network_receive_queue为None!')
        except Exception as e:
            self.logger.error(f'消息处理业务逻辑异常 (ID: {socket_id}): {e}')
            await self._send_error_response(socket_id, "消息处理失败")

    async def send_message(self, message: Dict[str, Any]):
        # 参数验证
        if not isinstance(message, dict):
            self.logger.error(f"发送消息格式错误: {type(message)}")
            return

        # 消息预处理，主要过滤并处理一些特殊消息
        if message.get('type') == 'user_login_response' and message.get('data', {}).get('success'):
            self.logger.info(f"用户登录成功:给消息注入现有房间信息")

            # 更新连接的用户名
            socket_id = message.get('socket_id', '')
            username = message.get('user_name', '')
            if socket_id and username:
                # 检查并清理重复登录
                await self.check_and_cleanup_duplicate_login(username, socket_id)
                # 更新连接用户名
                await self.update_connection_username(socket_id, username)

            # 直接使用预构建的可序列化房间信息
            message['data']['room_info'] = self.room_members_for_sync.copy()
            # 注入房间锁定座位信息（需要加锁保护）
            async with self.room_locked_seats_lock:
                message['data']['room_locked_seats'] = self.room_locked_seats.copy()

        elif message.get('type') == 'game_event_response' and message.get('data', {}).get('type') == 'init_game':
            room = message.get('data', {}).get('game_room', '')
            members = message.get('data', {}).get('members', [])
            if room in self.room_members:
                a = []
                async with self.room_members_lock:
                    for i in self.room_members[room]:
                        if i == 'is_playing':
                            continue
                        if i in members:
                            continue
                        else:
                            a.append(i)
                    for i in a:
                        del self.room_members[room][i]
                    self.room_members_for_sync[room]['members'] = members
                    self.room_members_for_sync[room]['member_count'] = 4
            print('清除后：', room, self.room_members_for_sync[room]['members'], self.room_members_for_sync[room]['member_count'])

        elif message.get('type') == 'game_event_response' and message.get('data', {}).get('type') == 'pick_pieces':
            room = message.get('room_name', '')
            members = self.room_members_for_sync.get(room, {}).get('members', [])
            print(f'收到pick_pieces消息,game_room:{room}', members)

        socket_id = message.get('socket_id', '')
        room_name = message.get('room_name', '')

        try:
        # 快速获取连接副本，减少锁持有时间
            async with self.active_connections_lock:
                connections_copy = dict(self.active_connections)
        except MemoryError:
            self.logger.error("内存不足，无法复制连接字典")
            return
        except Exception as e:
            self.logger.error(f"复制连接字典失败: {e}")
            return

        # 并发发送到所有连接的方法
        async def async_send_message(soc_id, conn: web.WebSocketResponse|None):
            try:
                if conn is None:  # 添加None检查
                    return soc_id, False, "连接对象为None"
                await conn.send_json(message)
                return soc_id, True, None
            except Exception as e:
                return soc_id, False, str(e)
        
        tasks = []
        try:
            # 向所有连接的客户端广播消息
            if room_name == 'hall':
                # 创建并发任务
                tasks = [
                    async_send_message(soc_id, conn_data[1])  # conn_data[1] 是 websocket
                    for soc_id, conn_data in connections_copy.items()
                ]
                self.logger.info(f"正在向所有 {len(tasks)} 个连接广播消息")

            # 向指定连接发送消息
            elif room_name == '':
                connection_data = connections_copy.get(socket_id)
                if not connection_data:
                    self.logger.error(f"❌ [Network] 单发消息但未找到目标连接 (ID: {socket_id})")
                    return
                username, websocket = connection_data
                tasks = [async_send_message(socket_id, websocket)]
                self.logger.info(f"正在向单个连接发送消息 (ID: {socket_id})")

            # 向指定房间广播消息
            else:
                async with self.room_members_lock:
                    room_data = self.room_members.get(room_name, {})
                    # 获取房间中的连接对象（排除 'is_playing' 键）
                    room_connections = [
                        (username, conn) for username, conn in room_data.items()
                        if username != 'is_playing' and conn is not None
                    ]

                # 创建发送任务，直接使用连接对象
                tasks = [
                    async_send_message(username, conn)
                    for username, conn in room_connections
                ]
                self.logger.info(f"正在向房间 {room_name} 的 {len(tasks)} 个成员广播消息")

        except Exception as e:
            self.logger.error(f"❌ [Network] 创建发送任务失败: {e}")
            return
        finally:
            #断开浅拷贝的连接字典，避免阻止有些连接对象的清理
            connections_copy.clear()

        # 处理空任务列表的情况
        if not tasks:
            self.logger.info("没有活跃连接，跳过广播")
            return

        # 并发执行所有发送任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = 0
        for result in results:
            if isinstance(result, tuple):
                socket_id, success, error = result
                if success:
                    success_count += 1
                else:
                    self.logger.error(f"发送消息时出错 (ID: {socket_id}): {error}")
            else:
                self.logger.error(f"广播任务异常: {result}")

        self.logger.info(f"发送完成: {success_count}/{len(tasks)} 成功")
    
    async def shutdown(self):
        """关闭所有连接并停止服务器"""
        self.is_shutting_down = True
        self.logger.info("开始关闭服务器...")
        
        async with self.active_connections_lock:
            # 关闭所有连接
            for socket_id, connection_data in self.active_connections.items():
                try:
                    username, ws = connection_data
                    await ws.close(code=1000, message=b"Server shutting down")
                    self.logger.info(f"关闭连接 (ID: {socket_id}, 用户: {username or '未登录'})")
                except Exception as e:
                    self.logger.error(f"关闭连接时出错 (ID: {socket_id}): {str(e)}")
            
            # 清空连接列表
            self.active_connections.clear()
        
        self.logger.info("所有连接已关闭")

    async def start_server(self, host='127.0.0.1', port=8080):
        """启动网络服务器（在网络线程中调用）

        包含：
        1. 创建web应用
        2. 注册WebSocket路由
        3. 启动网络发送队列处理任务
        4. 启动HTTP服务器

        Args:
            host: 服务器主机地址
            port: 服务器端口
        """
        try:
            # 1. 创建 web 应用
            self.logger.info("创建 Web 应用")
            app = web.Application()

            # 2. 注册路由
            self.logger.info("注册 WebSocket 路由")
            app.router.add_get('/ws', self.handle_new_websocket)

            # 3. 启动网络发送队列处理任务并等待其完全启动
            self.logger.info("启动网络发送队列处理任务")
            send_loop_ready = asyncio.Event()
            asyncio.create_task(self._network_send_loop(send_loop_ready))

            # 等待发送循环完全启动
            self.logger.info("等待网络发送队列处理循环启动完成...")
            await send_loop_ready.wait()
            self.logger.info("网络发送队列处理循环已就绪")

            # 启动连接清理任务
            self.logger.info("启动连接清理任务")
            asyncio.create_task(self._connection_cleanup_loop())

            # 4. 启动服务器
            self.logger.info(f"启动 Web 服务器 ({host}:{port})")
            await web._run_app(app, host=host, port=port)

        except Exception as e:
            self.logger.error(f"网络服务器启动失败: {e}")
            raise

    async def _network_send_loop(self, ready_event: Optional[asyncio.Event] = None):
        """网络发送队列处理循环（在网络线程中运行）

        Args:
            ready_event: 可选的事件对象，用于通知循环已启动
        """
        self.logger.info("网络发送队列处理循环启动")

        # 通知调用者循环已启动
        if ready_event:
            ready_event.set()
            self.logger.debug("网络发送队列处理循环就绪信号已发送")

        while not self.is_shutting_down:
            try:
                # 使用超时机制优化响应性能
                get_task = asyncio.create_task(self.network_send_queue.get())
                sleep_task = asyncio.create_task(asyncio.sleep(0.02))  # 优化：从100ms降低到20ms

                try:
                    done, _ = await asyncio.wait(
                        [get_task, sleep_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )

                    if get_task in done:
                        # 有消息到达，获取并处理
                        response_message = await get_task
                        sleep_task.cancel()  # 取消睡眠任务

                        socket_id = response_message.get('socket_id')
                        message_type = response_message.get('type')
                        self.logger.timing("NET_SEND", socket_id, message_type)  # 性能日志
                        
                        await self.send_message(response_message)
                        self.logger.info(f"✅ [Network] {message_type}消息已从队列移交发送程序")

                    else:
                        # 超时，取消获取任务，继续循环检查is_shutting_down状态
                        get_task.cancel()
                        continue  # 跳过消息处理，直接检查关闭状态

                except asyncio.CancelledError:
                    # 清理未完成的任务
                    if not get_task.done():
                        get_task.cancel()
                    if not sleep_task.done():
                        sleep_task.cancel()
                    break

            except Exception as e:
                self.logger.error(f"❌ [Network] 网络发送队列处理错误: {e}")
                import traceback
                self.logger.error(f"❌ [Network] 发送异常堆栈: {traceback.format_exc()}")
                # 继续处理下一个消息，不中断循环

        self.logger.info("网络发送队列处理循环停止")

    def _validate_complete_message_format(self, message: Dict[str, Any]) -> bool:
        """完整的消息格式验证

        验证七字段格式的必需字段和数据类型
        """
        try:
            # 1. 基本类型检查
            if not isinstance(message, dict):
                self.logger.error(f"消息不是字典类型: {type(message)}")
                return False

            # 2. 必需字段检查
            if 'type' not in message:
                self.logger.error("消息缺少type字段")
                return False

            if 'data' not in message:
                self.logger.error("消息缺少data字段")
                return False

            # 3. 字段类型检查
            if not isinstance(message['data'], dict):
                self.logger.error(f"data字段必须是字典类型: {type(message['data'])}")
                return False

            if not isinstance(message['type'], str):
                self.logger.error(f"type字段必须是字符串类型: {type(message['type'])}")
                return False

            # 4. type字段不能为空
            if not message['type'].strip():
                self.logger.error("type字段不能为空")
                return False

            return True

        except Exception as e:
            self.logger.error(f"消息格式验证异常: {e}")
            return False

    def _ensure_seven_fields(self, message: Dict[str, Any], socket_id: str) -> Dict[str, Any]:
        """确保消息包含完整的七字段格式

        为缺失的字段添加默认值
        """
        # 确保socket_id字段
        message['socket_id'] = socket_id

        # 确保其他字段存在，如果不存在则添加默认值
        if 'user_id' not in message:
            message['user_id'] = None
        if 'user_name' not in message:
            message['user_name'] = ''
        if 'room_name' not in message:
            message['room_name'] = ''

        return message

    async def _send_error_response(self, socket_id: str, error_message: str):
        """发送错误响应给客户端"""
        try:
            error_response = {
                'type': 'error',
                'data': {'message': error_message},
                'socket_id': socket_id,
            }

            # 直接发送，不通过消息队列
            if socket_id in self.active_connections:
                username, websocket = self.active_connections[socket_id]
                if websocket and not websocket.closed:
                    await websocket.send_json(error_response)
                    self.logger.info(f"已发送错误响应给客户端 (ID: {socket_id}): {error_message}")
                else:
                    self.logger.warning(f"连接已关闭，无法发送错误响应 (ID: {socket_id})")
            else:
                self.logger.warning(f"连接不存在，无法发送错误响应 (ID: {socket_id})")

        except Exception as e:
            self.logger.error(f"发送错误响应失败 (ID: {socket_id}): {e}")


    # ========================房间相关方法========================

    async def _handle_room_event(self, message: Dict[str, Any], socket_id: str, ws: web.WebSocketResponse):
        """处理房间相关事件"""
        self.logger.info(f"正在处理RoomEvent消息: {message['data']['type']}")
        response_message = await self._base_message_body('room', {}, socket_id, 'hall')

        try:
            event_data = message['data']
            if event_data['type'] == 'create':
                room_name = event_data['room_name']
                username = event_data['username']
                current_room = event_data['current_room']

                # 使用锁检查房间是否存在
                async with self.room_members_lock:
                    if room_name in self.room_members:
                        inner_data = {
                            'type': 'room_create_request',
                            'success': False,
                            'message': '房间已存在'
                        }
                        response_message['data'] = inner_data
                        response_message['room_name'] = ''
                        await self.send_message(response_message)
                        return

                # 根据current_room判断是否需要先离开房间
                if current_room:
                    room_remaining = await self.remove_room_member(current_room, username)
                    self.logger.info(f"玩家离开房间: {username} -> {current_room}")
                    if not room_remaining == None:
                        inner_data = {
                            'type': 'player_leave_room',
                            'room_name': current_room,
                            'player_name': username,
                            'room_remaining': room_remaining
                        }
                        response_message['data'] = inner_data
                        await self.send_message(response_message)

                # 更新房间成员字典以创建房间
                await self.update_room_members(room_name, {'is_playing': 0, username: ws})
                self.logger.info(f"房间创建成功: {room_name} (ID: {socket_id})")
                print(f"房间成员: {self.room_members}")
                print(f"同步数据: {self.room_members_for_sync}")

                inner_data = {
                    'type': 'room_create_request',
                    'success': True,
                    'message': '房间创建成功',
                    'room_name': room_name
                }
                response_message['data'] = inner_data
                response_message['room_name'] = ''
                await self.notice_room_created(room_name)

            elif event_data['type'] == 'enter_room':
                room_name = event_data['room_name']
                current_room = event_data['current_room']
                username = event_data['username']
                if self.room_members[room_name]['is_playing'] == 1:
                    inner_data = {'type': 'ignored'}
                    response_message['data'] = inner_data
                    await self.send_message(response_message)
                    return
                # 先尝试更新房间成员字典以加入房间
                result = await self.update_room_members(room_name, {username: ws})
                if result == 0: # 这属于边缘情况，服务器删除房间的指令未到达客户端前，用户点击了房间
                    inner_data = {'type': 'ignored'}
                    response_message['data'] = inner_data
                    await self.send_message(response_message)
                    return
                self.logger.info(f"玩家加入房间: {username} -> {room_name}")
                inner_data = {
                    'type': 'player_enter_room',
                    'room_name': room_name,
                    'player_name': username
                }
                response_message['data'] = inner_data
                await self.send_message(response_message)
                #根据current_room判断是否需要离开房间
                if current_room:
                    room_remaining = await self.remove_room_member(current_room, username)
                    if not room_remaining == None:
                        self.logger.info(f"玩家离开房间: {username} -> {current_room}")
                        inner_data = {
                            'type': 'player_leave_room',
                            'room_name': current_room,
                            'player_name': username,
                            'room_remaining': room_remaining
                        }
                        response_message['data'] = inner_data
                        await self.send_message(response_message)

            elif event_data['type'] == 'seat_click':
                seat_code = event_data['seat_code']
                click_user = event_data['click_user']
                room_name = event_data['room_name']
                self.logger.info(f"座位点击: {seat_code} (ID: {socket_id})")

                # 处理座位点击逻辑
                async with self.room_locked_seats_lock:
                    seats_status = self.room_locked_seats.get(room_name, [['', 0], ['', 0], ['', 0], ['', 0]])
                    inner_data = {
                            'type': '',
                            'seat_index': seat_code,
                            'click_user': click_user,
                            'room_name': room_name,
                            'last_seat': None
                        }
                    if seats_status[seat_code][0] != click_user and seats_status[seat_code][0] != '':
                        # 座位已被其他玩家占用
                        inner_data.update({'type': 'ignored'})
                    elif seats_status[seat_code][0] == '':
                        for i in range(4):
                            if click_user in seats_status[i]:
                                seats_status[i][0] = ''
                                seats_status[i][1] = 0
                                inner_data.update({'last_seat': i})
                        seats_status[seat_code][0] = click_user
                        seats_status[seat_code][1] = 0
                        self.room_locked_seats[room_name] = seats_status
                        inner_data.update({'type': 'seat_holded'})
                    elif seats_status[seat_code][0] == click_user:
                        seats_status[seat_code][0] = ''
                        seats_status[seat_code][1] = 0
                        self.room_locked_seats[room_name] = seats_status
                        inner_data.update({'type': 'seat_released'})
                    response_message['data'] = inner_data
                    await self.send_message(response_message)

            elif event_data['type'] == 'seat_switch':
                seat_code = event_data['seat_code']
                click_user = event_data['click_user']
                room_name = event_data['room_name']
                switch = event_data['switch']
                self.logger.info(f"座位锁定/解锁: {seat_code} (ID: {socket_id})")
                async with self.room_locked_seats_lock:
                    seats_status = self.room_locked_seats.get(room_name)
                    if not seats_status:
                        self.logger.warning(f"房间 {room_name} 不存在，无法锁定座位")
                        return
                    elif seats_status[seat_code][1] == 2: # 游戏中不允许锁定/解锁
                        return
                    elif switch == 1 and seats_status[seat_code][0] == click_user:
                        seats_status[seat_code][1] = 1
                        # 检查是否所有座位都已锁定，是则将所有座位状态设为2，开始游戏
                        j = 0
                        for i in seats_status:
                            if i[1] == 0:
                                break
                            j += 1
                        if j == 4:
                            async with self.room_members_lock: # 同时更新两个字典，保证数据一致性
                                self.room_members[room_name]['is_playing'] = 1
                                self.room_members_for_sync[room_name]['is_playing'] = 1
                            for i in seats_status:
                                i[1] = 2
                            init_message ={
                                'type': 'game_event',
                                'data': {'type': 'init_game', 'seats_status': seats_status, 'room_name': room_name},
                                'room_name': 'hall'
                            }
                            await self.network_receive_queue.put(init_message)
                    elif switch == 0 and seats_status[seat_code][0] == click_user:
                        seats_status[seat_code][1] = 0
                    else:
                        self.logger.info(f'座位锁定/解锁失败: {seat_code} (ID: {socket_id})')
                        return
                    response_message['data'] = event_data
                    await self.send_message(response_message)
                    
        except Exception as e:
            self.logger.error(f'❌ [Network] 房间事件处理异常 (ID: {socket_id}): {e}')
            inner_data = {
                'type': 'room_create_request',
                'success': False,
                'message': '房间事件处理失败'
            }
            response_message['data'] = inner_data
        finally:
            await self.send_message(response_message)


    async def notice_room_created(self, room_name: str):
        """通知所有客户端有新房间创建"""
        response_message = await self._base_message_body('room', {}, '', 'hall')
        inner_data = {
            'type': 'room_created',
            'data': [room_name,
                    {room_name: self.room_members_for_sync[room_name]},
                    {room_name: [['', 0], ['', 0], ['', 0], ['', 0]]}]
        }
        response_message['data'] = inner_data
        await self.send_message(response_message)


    async def update_room_members(self, room_name, updates):
        """更新房间成员信息（同时维护两个字典）"""
        async with self.room_members_lock:
            # 更新主字典
            if room_name not in self.room_members:
                if 'is_playing' in updates: # 如果是创建房间，初始化房间字典
                    self.room_members[room_name] = {}
                    # 同时初始化房间座位锁定信息
                    async with self.room_locked_seats_lock:
                        self.room_locked_seats[room_name] = [['', 0], ['', 0], ['', 0], ['', 0]]
                else: # 极端情况下，服务器已经删除某房间但未传达到客户端时，有人点击房间申请加入，无视申请
                    return 0 # 通知调用者房间已不存在

            self.room_members[room_name].update(updates)

            # 同步更新可序列化字典（内置同步逻辑）
            room_data = self.room_members[room_name]
            members = [username for username in room_data.keys() if username != 'is_playing']
            self.room_members_for_sync[room_name] = {
                'is_playing': room_data.get('is_playing', 0),
                'members': members,
                'member_count': len(members)
            }
            return 1 # 通知调用者房间正常更新/创建

    async def remove_room_member(self, room_name, username) -> int|None:
        """移除房间成员（同时维护三个字典）"""
        async with self.room_members_lock:
            if room_name in self.room_members and username in self.room_members[room_name]:
                # 从主字典移除成员
                del self.room_members[room_name][username]

                # 如果房间没有成员了，删除整个房间
                room_data = self.room_members[room_name]
                members = [k for k in room_data.keys() if k != 'is_playing']

                if not members:
                    del self.room_members[room_name]
                    if room_name in self.room_members_for_sync:
                        del self.room_members_for_sync[room_name]
                    # 删除房间座位信息（需要加锁）
                    async with self.room_locked_seats_lock:
                        if room_name in self.room_locked_seats:
                            del self.room_locked_seats[room_name]
                    return 0
                else:
                    # 更新可序列化字典
                    self.room_members_for_sync[room_name] = {
                        'is_playing': room_data.get('is_playing', 0),
                        'members': members,
                        'member_count': len(members)
                    }
        async with self.room_locked_seats_lock:
            if room_name in self.room_locked_seats:
                for i in range(4):
                    if self.room_locked_seats[room_name][i][0] == username:
                        self.room_locked_seats[room_name][i][0] = ''
                        self.room_locked_seats[room_name][i][1] = 0
        return 1

    async def _base_message_body(self, message_type: str, data: Dict[str, Any], socket_id: str, room_name: str) -> Dict[str, Any]:
        """生成基础的消息体"""
        m_type = 'room_event_response' if message_type == 'room' else 'chat_distribution'
        return {
            'type': m_type,
            'data': data,
            'socket_id': socket_id,
            'room_name': room_name
        }
    