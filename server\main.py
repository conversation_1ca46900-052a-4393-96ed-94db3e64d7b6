
import asyncio
from message_hub import MessageHub
from network import ConnectionManager
from admin_console import AdminConsole
from security_validator import SecurityValidator
from server.data.data_center import create_and_run_data_center
from tool_kit import ThreadedEventLoop, Logger, PasswordPreValidator


async def async_main():
    """异步主函数"""
    # 创建单个日志实例
    logger = Logger()
    security_validator = SecurityValidator(PasswordPreValidator())
    logger.info("=== 4inWar 服务器启动 ===")

    # 初始化变量，避免在finally块中出现未绑定错误
    connection_manager = None
    network_thread = None
    data_thread = None
    message_hub = None
    message_hub_task = None
    admin_console = None

    try:
        # === 第一阶段：内部消息总线就绪 ===
        logger.info("=== 第一阶段：内部消息总线启动 ===")

        # 1. 创建消息队列
        logger.info("创建消息队列")
        network_receive_queue = asyncio.Queue(maxsize=1000) # 网络线程 → 主线程MessageHub
        pending_queue = asyncio.Queue(maxsize=1000)      # MessageHub → 数据线程
        response_queue = asyncio.Queue(maxsize=1000)     # 数据线程 → MessageHub
        network_send_queue = asyncio.Queue(maxsize=1000) # MessageHub → 网络线程
        logger.info("消息队列创建完成")

        # 2. 创建线程管理器
        logger.info("创建线程管理器")
        network_thread = ThreadedEventLoop("network_thread", logger)
        data_thread = ThreadedEventLoop("data_thread", logger)

        # 3. 启动必要线程和事件循环
        logger.info("启动工作线程")
        network_thread.start()  # 创建线程并启动事件循环，但还没有指示线程去执行具体任务
        data_thread.start()

        # 4. 创建网络管理器
        logger.info("初始化网络管理器")
        connection_manager = ConnectionManager(logger, network_receive_queue, network_send_queue)

        # 5. 在data_thread事件循环中启动数据中心
        logger.info("启动数据中心")
        data_thread.submit_task(create_and_run_data_center(logger, pending_queue, response_queue))

        # 6. 创建admin控制台
        logger.info("创建管理员控制台")
        admin_console = AdminConsole(logger, security_validator)

        # 7. 创建并启动主线程MessageHub
        logger.info("启动主线程MessageHub")
        message_hub = MessageHub(logger, admin_console, network_receive_queue, pending_queue, response_queue, network_send_queue)

        # 8. 设置admin_console的MessageHub引用
        admin_console.set_message_hub(message_hub)

        message_hub_task = asyncio.create_task(message_hub.start())

        # 9. 等待线程初始化完成
        logger.info("等待线程初始化完成")
        await asyncio.sleep(1)

        logger.info("第一阶段启动完成，开始管理员认证")

        # 10. 管理员认证
        auth_success = await admin_console.phase_one_loop(logger)

        if not auth_success:
            logger.info("管理员认证失败或选择退出，服务器停止")
            return

        # === 第二阶段：网络模块启动 ===
        logger.info("=== 第二阶段：网络模块启动 ===")

        # 11. 在网络线程中启动 web 服务器
        logger.info("启动网络服务器")
        network_thread.submit_task(connection_manager.start_server(host='127.0.0.1', port=8080))

        logger.info("第二阶段启动完成，服务器就绪")

        # 12. 第二阶段事件循环：正常服务
        await admin_console.phase_two_loop()
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        print(f"服务器启动失败: {e}")
    finally:
        logger.info("=== 开始服务器关闭流程 ===")
        try:
            # 停止主线程MessageHub
            if message_hub is not None:
                logger.info("停止主线程MessageHub")
                await message_hub.stop()

            # 取消主线程MessageHub任务
            if message_hub_task is not None:
                logger.info("取消主线程MessageHub任务")
                message_hub_task.cancel()
                try:
                    await message_hub_task
                except asyncio.CancelledError:
                    logger.info("主线程MessageHub任务已取消")

            # 停止网络连接管理器
            if connection_manager is not None and network_thread is not None:
                logger.info("停止网络连接管理器")
                shutdown_future = network_thread.submit_task(connection_manager.shutdown())
                if shutdown_future:
                    shutdown_future.result(timeout=10.0)  # 等待关闭完成

            # 停止工作线程
            if network_thread is not None or data_thread is not None:
                logger.info("停止工作线程")
                if network_thread is not None:
                    network_thread.stop()
                if data_thread is not None:
                    data_thread.stop()

            logger.info("=== 服务器关闭完成 ===")
            print("服务已停止")
        except Exception as e:
            logger.error(f"服务器关闭过程中出错: {e}")
            print(f"关闭过程中出错: {e}")
        finally:
            # 清理日志系统
            logger.cleanup()

def main():
    """程序入口点"""
    try:
        # 运行异步主函数
        asyncio.run(async_main())
    except KeyboardInterrupt:
        # 在这里处理 Ctrl+C，确保优雅退出
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常退出: {e}")
        raise

if __name__ == "__main__":
    main()
