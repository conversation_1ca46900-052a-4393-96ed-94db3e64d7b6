"""
UI游戏核心模块
专门处理游戏相关的UI事务和消息处理
"""
from PySide6.QtSvgWidgets import QGraphicsSvgItem
from PySide6.QtCore import QObject, Qt, QTimer
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtCore import QObject, Qt
from PySide6.QtWidgets import QGraphicsSimpleTextItem, QGraphicsPixmapItem
from PySide6.QtGui import QFont, QColor, QTransform, QPixmap, QPainter, QBrush


class GameConfig:
    """游戏基础配置数据"""
    
    # 游戏区域坐标
    AREA_0 = [((45 * 6 + 13) + i * 81, 45 * 6 + 81 * 5 + 7 + j * 45) for i in range(5) for j in range(6)]
    AREA_1 = [(45 * 6 + 81 * 5 - 5 + i * 45, 45 * 6 + 25 + j * 81) for i in range(6) for j in range(5)]
    AREA_2 = [((45 * 6 + 13) + i * 81, 7 + j * 45) for i in range(5) for j in range(6)]
    AREA_3 = [(-5 + i * 45, 45 * 6 + 25 + j * 81) for i in range(6) for j in range(5)]
    AREA_MIDDLE = [(283 + i * 162, 295 + j * 162) for i in range(3) for j in range(3)]
    
    # 所有区域合并
    ALL_AREAS = AREA_0 + AREA_1 + AREA_2 + AREA_3 + AREA_MIDDLE
    
    # 军营坐标
    BARRACKS = [
        (364, 727), (526, 727), (445, 772), (364, 817), (526, 817),
        (85, 376), (175, 376), (130, 457), (85, 538), (175, 538),
        (364, 97), (526, 97), (445, 142), (364, 187), (526, 187),
        (715, 376), (805, 376), (760, 457), (715, 538), (805, 538)
    ]
    
    # 总部坐标
    HEADQUARTERS = [
        (364, 907), (526, 907), (-5, 376), (-5, 538), 
        (364, 7), (526, 7), (895, 376), (895, 538)
    ]
    
    # 死亡区域坐标
    AREA_0_DEAD = [(685 + i * 65, 682 + j * 45) for i in range(4) for j in range(6)]
    AREA_1_DEAD = [(670 + i * 45, 217 - j * 65) for j in range(4) for i in range(6)]
    AREA_2_DEAD = [(205 - i * 65, 232 - j * 45) for i in range(4) for j in range(6)]
    AREA_3_DEAD = [(220 - i * 45, 697 + j * 65) for j in range(4) for i in range(6)]
    
    # 路点坐标
    ROAD_POINT = [(445, 727), (364, 772), (526, 772), (445, 817), (283, 907), (364, 907), (445, 907),
                (526, 907), (607, 907), (715, 457), (760, 538), (760, 376), (805, 457), (895, 619),
                (895, 538), (895, 457), (895, 376), (895, 295), (445, 187), (526, 142), (364, 142),
                (445, 97), (607, 7), (526, 7), (445, 7), (364, 7), (283, 7), (175, 457), (130, 376),
                (130, 538), (85, 457), (-5, 295), (-5, 376), (-5, 457), (-5, 538), (-5, 619)]

    # 转向点坐标
    TURN_POINT = [(283, 682), (607, 682), (670, 619), (670, 295), (283, 232), (607, 232), (220, 295), (220, 619)]

    # 棋子原始缩放因子
    PIECE_ORG_SCALE = 55 / 31
    
    @classmethod
    def get_area_list(cls, area_id: int) -> list:
        """获取指定的区域坐标
                
        Args:
            area_id: 区域ID　-> 0-3: 0-3号区域; 4: 中间区域; 5: 军营; 6: 总部; 7: 所有区域
        """
        areas = [cls.AREA_0, cls.AREA_1, cls.AREA_2, cls.AREA_3]
        if 0 <= area_id < 4:
            return areas[area_id]
        elif area_id == 4:
            return cls.AREA_MIDDLE
        elif area_id == 5:
            return cls.BARRACKS
        elif area_id == 6:
            return cls.HEADQUARTERS
        else:
            return cls.ALL_AREAS
    
    @classmethod
    def get_dead_area_list(cls, area_id: int) -> list:
        """获取指定玩家的死亡区域坐标
        
        Args:
            player_id: 死亡区域ID -> 0-3: 0-3号玩家
        """
        dead_areas = [cls.AREA_0_DEAD, cls.AREA_1_DEAD, cls.AREA_2_DEAD, cls.AREA_3_DEAD]
        if 0 <= area_id < 4:
            return dead_areas[area_id]
        return []
    
    @classmethod
    def get_road_point(cls) -> list:
        """获取路点坐标"""
        return cls.ROAD_POINT
    
    @classmethod
    def get_turn_point(cls) -> list:
        """获取转向点坐标"""
        return cls.TURN_POINT
    
    @classmethod
    def get_piece_org_scale(cls) -> float:
        """获取棋子原始缩放因子"""
        return cls.PIECE_ORG_SCALE


class ActiveSign(QGraphicsPixmapItem):
    """活跃标志类 - 实现呼吸透明度循环效果"""

    def __init__(self, image_path=None, parent=None):
        """初始化活跃标志

        Args:
            image_path: 图片文件路径，如果为None则创建空的标志
            parent: 父对象
        """
        super().__init__(parent)

        # 加载图片
        if image_path:
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                self.setPixmap(pixmap)
            else:
                print(f"警告: 无法加载图片文件: {image_path}")
                # 创建一个默认的小圆点作为标志
                self._create_default_pixmap()
        else:
            # 创建默认标志
            self._create_default_pixmap()

        # 初始化动画
        self._setup_breathing_animation()

    def _create_default_pixmap(self):
        """创建默认的活跃标志图片（小圆点）"""
        pixmap = QPixmap(20, 20)
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QBrush(QColor(46, 213, 201)))  # 主题绿色
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(2, 2, 16, 16)
        painter.end()

        self.setPixmap(pixmap)

    def _setup_breathing_animation(self):
        """设置呼吸动画"""
        # 创建一个自定义的透明度属性动画
        self._opacity_value = 1.0

        # 使用定时器来实现呼吸效果
        self.breathing_timer = QTimer()
        self.breathing_timer.timeout.connect(self._update_breathing)
        self.breathing_timer.setInterval(50)  # 50ms更新一次

        # 呼吸参数
        self._breathing_step = 0
        self._breathing_speed = 0.18  # 呼吸速度
        self._min_opacity = 0.3
        self._max_opacity = 1.0

    def _update_breathing(self):
        """更新呼吸效果"""
        import math
        # 使用正弦波来创建平滑的呼吸效果
        self._breathing_step += self._breathing_speed

        # 计算当前透明度值
        opacity_range = self._max_opacity - self._min_opacity
        opacity = self._min_opacity + opacity_range * (math.sin(self._breathing_step) * 0.5 + 0.5)

        # 应用透明度
        self.setOpacity(opacity)

    def start_breathing(self):
        """开始呼吸动画"""
        if not self.breathing_timer.isActive():
            self.breathing_timer.start()
            print("活跃标志开始呼吸动画")

    def stop_breathing(self):
        """停止呼吸动画"""
        if self.breathing_timer.isActive():
            self.breathing_timer.stop()
            self.setOpacity(1.0)  # 恢复完全不透明
            print("活跃标志停止呼吸动画")

    def set_breathing_speed(self, speed: float):
        """设置呼吸速度

        Args:
            speed: 呼吸速度，值越大呼吸越快 (建议范围: 0.01-0.1)
        """
        self._breathing_speed = speed

    def set_opacity_range(self, min_opacity: float, max_opacity: float):
        """设置透明度范围

        Args:
            min_opacity: 最小透明度 (0.0-1.0)
            max_opacity: 最大透明度 (0.0-1.0)
        """
        self._min_opacity = max(0.0, min(1.0, min_opacity))
        self._max_opacity = max(0.0, min(1.0, max_opacity))

        # 确保最小值不大于最大值
        if self._min_opacity > self._max_opacity:
            self._min_opacity, self._max_opacity = self._max_opacity, self._min_opacity


class UIGameCore(QObject):
    """UI游戏核心类
    
    负责处理所有游戏相关的UI逻辑：
    - 游戏事件消息处理
    - 游戏状态管理
    - 游戏UI控制
    """
    
    def __init__(self, main_window, logger):
        """初始化UI游戏核心
        
        Args:
            main_window: 主窗口引用
            logger: 日志记录器
        """
        super().__init__()
        self.main_window = main_window
        self.logger = logger

        self.my_name = None
        self.my_seat = 0
        self.my_last_seat = 0
        self.piece_obj_dic = {}  # 保存所有的棋子对象的字典
        self.pick_phase = False
        self.pieces_picked = []
        self.my_piece = None
        self.enemy_piece = None
        self.rush_lock = 0  # 棋子行动一次后赋值为1，表示被锁定，不可变更选择
        self.active_sign = ActiveSign('./client/image/active_sign.svg')
        
        # 连接游戏相关信号
        self._connect_game_signals()
        
        self.logger.info("UI游戏核心模块初始化完成")
    
    def _connect_game_signals(self):
        """连接游戏相关信号"""
        # 连接游戏事件响应信号
        self.main_window.message_hub.signals.game_event_response.connect(self.handle_game_event)
        self.main_window.message_hub.signals.scene_click_signal.connect(self.handle_scene_click)
        
        self.logger.debug("游戏信号连接完成")
    
    # ==================== 游戏消息处理方法 ====================
    
    def handle_game_event(self, message):
        """处理游戏事件的统一入口"""
        data = message.get("data", {})
        event_type = data.get("type")
        self.logger.info(f"处理游戏事件: {event_type}")
        
        if event_type == "init_game":
            self._handle_game_init(data)
        elif event_type == "init_pieces":
            self.init_pieces(data)
        elif event_type == "pick_pieces":
            self.pick_pieces(data)
        elif event_type == "start_game":
            self.start_game(data)
        elif event_type == "end_game":
            self._handle_game_ended(data)
        elif event_type == "game_action":
            self._handle_game_action(data)
        else:
            self.logger.warning(f"未知的游戏事件类型: {event_type}")
    
    def _handle_game_init(self, data):
        """处理游戏开始时房间相关事务"""
        self.my_name = self.main_window.user_data['username']
        room_name = data.get("game_room")
        members = data.get("members")
        self.logger.info(f"游戏开始: 房间={room_name}, 成员={members}")

        # 更新房间状态为游戏中
        self.main_window.move_room_to_playing(room_name, members)
        
        # 如果当前用户在游戏中，启动游戏界面
        if self.my_name in members:
            self.my_last_seat = self.my_seat
            self.my_seat = members.index(self.my_name)
            # 同步更新MainWindow的my_seat
            self.main_window.my_seat = self.my_seat
            self.main_window.view.rotate(90 * (self.my_seat - self.my_last_seat))
            print('我转了{}度'.format(90 * (self.my_seat - self.my_last_seat)))
            self.init_game(members)
        elif self.main_window.my_room == room_name:
            self.logger.info(f'{room_name}开始游戏了，无座位之人退出房间')
            self.main_window.my_room = ''
            self.main_window.tabs.removeTab(1)
    
    def _handle_game_ended(self, data):
        """处理游戏结束消息"""
        room_name = data.get("game_room")
        members = data.get("members")
        self.logger.info(f"游戏结束: 房间={room_name}, 成员={members}")
        
        # 更新房间状态为等待中
        self.main_window.move_room_to_waiting(room_name)
        
        # 如果当前用户在游戏中，结束游戏界面
        if self.main_window.user_data['username'] in members:
            self.end_game()
    
    def _handle_game_action(self, data):
        """处理游戏动作消息"""
        action_type = data.get("action_type")
        self.logger.info(f"处理游戏动作: {action_type}")
        
        # TODO: 根据具体的游戏动作类型进行处理
        # 例如：棋子移动、攻击、防御等
        pass
    

    # ==================== 游戏状态控制方法 ====================

    def init_game(self, members):
        """游戏开始前，进行初始化"""
        self.logger.info("启动游戏界面")
        self.main_window.scene.freeze()
        
        # 更新游戏状态及初始设置
        self.main_window.I_am_playing = True
        self.pick_phase = True
        self.my_piece = None
        self.enemy_piece = None
        self.piece_obj_dic = {}
        self.rush_lock = 0
        items = self.main_window.scene.items()
        for i in items:
            if i != self.main_window.scene.background_svg:  # 排除棋盘对象
                self.main_window.scene.removeItem(i)
                del i
        self.set_name_label(members)


    def end_game(self):
        """游戏结束，显示遮罩"""
        self.logger.info("结束游戏界面")
        # 显示游戏视图遮罩
        self.main_window.view_mask.show()
        # 更新游戏状态
        self.main_window.I_am_playing = False
        # 显示座位选择框架
        self.main_window.seat_choose_frame.show()
        # 显示游戏结束引导标志
        self.main_window.show_guide_signs(3)
        # 将房间移动到等待状态
        self.main_window.move_room_to_waiting(self.main_window.my_room)
    
    def pause_game(self):
        """暂停游戏"""
        self.logger.info("暂停游戏")
        # TODO: 实现游戏暂停逻辑
        pass
    
    def resume_game(self):
        """恢复游戏"""
        self.logger.info("恢复游戏")
        # TODO: 实现游戏恢复逻辑
        pass


    # ==================== 棋盘交互方法 ====================

    def init_pieces(self, data):
        """初始化棋子"""
        piece_init_dict = data.get("piece_data")
        self.logger.debug("初始化棋子")
        for i in piece_init_dict:
            Piece(i, piece_init_dict[i], self)  # 实例化棋子
        self.main_window.seat_choose_frame.hide() # 隐藏座位选择框架
        self.main_window.view_mask.hide() # 隐藏游戏视图遮罩
        self.main_window.show_guide_signs(1) # 显示游戏开始引导标志


    def pick_pieces(self, data):
        piece_id = data.get("piece_id")
        position = data.get("position")
        pick_seat = data.get("pick_seat")
        piece = self.piece_obj_dic[piece_id]
        if pick_seat == self.my_seat:
            self.pieces_picked.append(piece)
            print(f'我已经选了{len(self.pieces_picked)}个棋子了',self.pieces_picked)
        piece._update(pick_seat, position, 2, True)


    def start_game(self, data):
        """游戏开始，显示棋子"""
        self.logger.info("游戏开始")
        self.pick_phase = False
        rejust_pos = data.get("rejust_pos")
        first_seat = data.get("first_seat")
        for i in rejust_pos:
            piece = self.piece_obj_dic[i[0]]
            piece._update(piece.owner, i[1], piece.status)
        self.main_window.show_guide_signs(2)
        if first_seat == self.my_seat:
            QTimer.singleShot(2200, lambda: self.show_active_sign(first_seat))
        

    def handle_scene_click(self, button, scene_pos):
        """处理棋盘点击事件"""
        self.logger.debug(f"棋盘点击: 按钮={button}, 位置=({scene_pos.x()}, {scene_pos.y()})")

        item = self.main_window.scene.itemAt(scene_pos, QTransform())
        if self.pick_phase:
            if isinstance(item, Piece) and item.owner != None and (item.owner - self.my_seat) % 2 != 0 and item.status == 1:
                if len(self.pieces_picked) >= 2 or len(self.pieces_picked) == 1 and self.pieces_picked[0].color == item.color:
                    return
                message = {
                    'type': 'game_event',
                    'data': {'type': 'pick_pieces', 'piece_id': item.id, 'position': item.position, 'rank': item.rank, 'owner': item.owner},
                    'user_name': self.my_name,
                    'room_name': self.main_window.my_room
                }
                self.main_window.message_hub.send_to_business_thread(message)
        elif button == 'r':
            if self.my_piece and self.rush_lock == 0:
                print('取消了对我方棋子rank:{} color:{}的选择'.format(self.my_piece.rank, self.my_piece.color))
                self.my_piece = None
        elif isinstance(item, Piece):
            if item.owner == self.my_seat and item.rank != '11' and item.status == 1 and self.rush_lock == 0:
                self.my_piece = item
                print('选择了我方棋子，rank:{} color:{}'.format(item.rank, item.color))
            elif self.my_piece:
                if item.owner is None:
                    print('打算前进到{}处'.format(item.pos))
                    self.enemy_piece = item
                elif item.status == 2 and (item.owner - self.my_seat) % 2 != 0:  # 当是敌方人质棋时
                    print('我方能攻击到rank:{} color:{}吗？'.format(item.rank, item.color))
                    self.enemy_piece = item
                elif item.owner is not None and (item.owner - self.my_seat) % 2 != 0 and item.status == 1:  # 当是敌方棋子时
                    print('我方能攻击到rank:{} color:{}吗？'.format(item.rank, item.color))
                    self.enemy_piece = item
                else:
                    return
                # TODO: 实现寻路判断方法
                # self.navigation(self.my_piece, self.enemy_piece)  # 调用寻路判断方法，看两棋能否按规则接触
                print('选择了目标棋子，准备移动')

    
    def _truncate_name(self, name, max_length=6):
        """截断名字，汉字计2，ASCII字符计1，总长度不超过max_length"""
        if not name:
            return ""

        current_length = 0
        result = ""

        for char in name:
            # 判断是否为汉字（简单判断：Unicode编码范围）
            if '\u4e00' <= char <= '\u9fff':
                char_length = 2  # 汉字计为2
            else:
                char_length = 1  # ASCII字符计为1

            if current_length + char_length <= max_length:
                result += char
                current_length += char_length
            else:
                break

        return result

    def set_name_label(self, player_list):
        """设置玩家名称标签"""
        self.scale = self.main_window.scale
        for i in range(4):
            label = QGraphicsSimpleTextItem()
            label.setBrush(QColor("#553810"))  # 使用自定义棕色
            # 截断名字长度
            truncated_name = self._truncate_name(player_list[i])
            label.setText(truncated_name)
            label.setFont(QFont("黑体", 14 * self.scale))
            self.main_window.scene.addItem(label)
            label.setTransformOriginPoint(60 * self.scale / 2, 20 * self.scale / 2)
            if i == 0:
                label.setPos(375 * self.scale, 655 * self.scale)
                label.setRotation(0)
            elif i == 1:
                label.setPos(635 * self.scale, 530 * self.scale)
                label.setRotation(-90)
            elif i == 2:
                label.setPos(508 * self.scale, 270 * self.scale)
                label.setRotation(180)
            else:
                label.setPos(250 * self.scale, 395 * self.scale)
                label.setRotation(90)

    def show_active_sign(self, code):
        self.pic_pos = [(439.5, 538), (543, 434.5), (439.5, 331), (336, 434.5)]
        self.main_window.scene.addItem(self.active_sign)
        self.active_sign.setTransformOriginPoint(65 / 2, 75 / 2)
        self.active_sign.setRotation(-90 * code)
        self.active_sign.setScale(self.scale * 1.2)
        print('放置并旋转到{}处'.format(code))
        self.active_sign.setPos(self.pic_pos[code][0] * self.scale, self.pic_pos[code][1] * self.scale)
        self.active_sign.start_breathing()
        self.main_window.scene.activate()
        print('放好了')


    # ==================== 清理方法 ====================
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("清理UI游戏核心资源")
        
        # 断开信号连接
        try:
            self.main_window.message_hub.signals.game_event_response.disconnect(self.handle_game_event)
        except Exception as e:
            self.logger.warning(f"断开游戏信号连接时出错: {e}")
        
        self.logger.info("UI游戏核心资源清理完成")



class Piece(QGraphicsSvgItem):  # 棋子类，继承于QGraphicsSvgItem类，可绑定矢量图片
    def __init__(self, id, data_list, game_core):
        super(Piece, self).__init__()
        # self.setOpacity(0.5)
        self.id = id
        self.owner = data_list[0]
        self.color = data_list[1]
        self.rank = data_list[2]
        self.position = tuple(data_list[3])
        self.status = data_list[4]  # 状态判断标志位，0：死亡　1：存活　2：人质
        self.visible = False
        if self.owner == game_core.main_window.my_seat:
            self.setCursor(Qt.CursorShape.PointingHandCursor)
            self.visible = True
        self.game_core = game_core
        self.setTransformOriginPoint(31 / 2, 17 / 2)  # 变换基准点处于物体自身坐标系，影响一切变形效果，包括缩放和旋转
        self.setScale(GameConfig.get_piece_org_scale())  # 先变换为初始大小
        self.setScale(GameConfig.get_piece_org_scale() * game_core.main_window.scale)  # 再变换为所需大小
        self.game_core.piece_obj_dic[id] = self
        self.game_core.main_window.scene.addItem(self)  # 将棋子对象置入场景，才能显示出来
        self.layout()

    def _update(self, owner, position, status, visible='remain'):  # 更新棋子信息
        '''
        args:
            data_list: [owner, position, status -- 0:死亡 1:存活 2:人质]
        '''
        self.owner = owner
        self.position = tuple(position)
        self.status = status  # 状态判断标志位，0：死亡　1：存活　2：人质
        if status != 1:
            self.setCursor(Qt.CursorShape.ArrowCursor)
        if not visible == 'remain':
            self.visible = visible
        self.layout()

    def layout(self):
        self.pic_update()
        self.setPos((self.position[0] * self.game_core.main_window.scale + 31 * (GameConfig.get_piece_org_scale() * self.game_core.main_window.scale - 1) / 2),
                     (self.position[1] * self.game_core.main_window.scale + 17 * (GameConfig.get_piece_org_scale() * self.game_core.main_window.scale - 1) / 2))
        self.rotate()

    def pic_update(self):
        if self.owner is None:
            pic_name = '.\\client\\image\\pieces\\blank.svg'
            self.setOpacity(0.5)
        elif self.visible:
            pic_name = '.\\client\\image\\pieces\\' + self.color + '\\' + self.rank + '.svg'
        else:
            pic_name = '.\\client\\image\\pieces\\' + self.color + '\\' + 'back.svg'
        self.render = QSvgRenderer()
        if self.render.load(pic_name):
            self.setSharedRenderer(self.render)
        else:
            print(f"无法加载SVG文件: {pic_name}")

    def rotate(self):  # 判断棋子的阵营及所处位置，执行正确地旋转
        pos = self.position
        center_area = GameConfig.get_area_list(4)
        if self.owner is None:  # 对空白棋的旋转策略
            if pos in center_area or pos in self.which_area(self.game_core.my_seat, 0) or pos in self.which_area(self.game_core.my_seat, 1):
                self.setRotation(-90 * self.game_core.my_seat)  # 在自身、友方区域及中央区域按己方棋子处理
            elif pos in self.which_area(self.game_core.my_seat, 2):
                self.setRotation(-90 * (self.game_core.my_seat - 1))  # 在敌方区域，空白棋子相对己方棋子少转90度
        elif pos in center_area or pos in self.which_area(self.owner, 0) or pos in self.which_area(self.owner, 1):
            self.setRotation(-90 * self.owner)  # 各方棋子在自身区域、中央区域，正常旋转
        elif pos in self.which_area((lambda x: x + 1 if x < 3 else 0)(self.owner), 0):
            self.setRotation(-90 * (self.owner - 1))  # 棋子在右手侧敌人区域时，按左手侧敌人的角度旋转
        elif pos in self.which_area((lambda x: x - 1 if x > 0 else 3)(self.owner), 0):
            self.setRotation(-90 * (self.owner + 1))  # 棋子在左手侧敌人区域时，按右手侧敌人的角度旋转
        else:
            self.setRotation(-90 * self.owner)  # 其他情况，正常旋转

    def which_area(self, owner: int, num: int) -> list:
        '''
        args:
            owner: 棋子所属玩家
            num: 传入的num值决定返回列表　0:己方区域　1：友方区域　2：敌方区域
        '''
        # 确保 owner 在有效范围内
        if not (0 <= owner <= 3):
            return []

        dic = {0: GameConfig.get_area_list(0) + GameConfig.get_dead_area_list(0),
               1: GameConfig.get_area_list(1) + GameConfig.get_dead_area_list(1),
               2: GameConfig.get_area_list(2) + GameConfig.get_dead_area_list(2),
               3: GameConfig.get_area_list(3) + GameConfig.get_dead_area_list(3)
        }
        if num == 0:  # 调用者请求返回code方的自身区域
            return dic[owner]
        elif num == 1:  # 调用者请求返回code方的友方区域
            if owner < 2:
                return dic[owner + 2]
            else:
                return dic[owner - 2]
        elif num == 2:  # 调用者请求返回code方的敌方区域
            if 0 < owner < 3:
                return dic[owner + 1] + dic[owner - 1]
            elif owner == 0:
                return dic[1] + dic[3]
            else:  # owner == 3
                return dic[0] + dic[2]
        else:
            # 对于其他 num 值，返回空列表
            return []


    def __del__(self):  # 察看棋子对象是否被删除，释放内存
        print('del', self.id)

