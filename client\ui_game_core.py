"""
UI游戏核心模块
专门处理游戏相关的UI事务和消息处理
"""
from PySide6.QtSvgWidgets import QGraphicsSvgItem
from PySide6.QtCore import QObject, Qt, QTimer
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtCore import QObject, Qt
from PySide6.QtWidgets import QGraphicsSimpleTextItem, QGraphicsPixmapItem
from PySide6.QtGui import QFont, QColor, QTransform, QPixmap, QPainter, QBrush


class GameConfig:
    """游戏基础配置数据"""
    
    # 游戏区域坐标
    AREA_0 = [((45 * 6 + 13) + i * 81, 45 * 6 + 81 * 5 + 7 + j * 45) for i in range(5) for j in range(6)]
    AREA_1 = [(45 * 6 + 81 * 5 - 5 + i * 45, 45 * 6 + 25 + j * 81) for i in range(6) for j in range(5)]
    AREA_2 = [((45 * 6 + 13) + i * 81, 7 + j * 45) for i in range(5) for j in range(6)]
    AREA_3 = [(-5 + i * 45, 45 * 6 + 25 + j * 81) for i in range(6) for j in range(5)]
    AREA_MIDDLE = [(283 + i * 162, 295 + j * 162) for i in range(3) for j in range(3)]
    
    # 所有区域合并
    ALL_AREAS = AREA_0 + AREA_1 + AREA_2 + AREA_3 + AREA_MIDDLE
    
    # 军营坐标
    BARRACKS = [
        (364, 727), (526, 727), (445, 772), (364, 817), (526, 817),
        (85, 376), (175, 376), (130, 457), (85, 538), (175, 538),
        (364, 97), (526, 97), (445, 142), (364, 187), (526, 187),
        (715, 376), (805, 376), (760, 457), (715, 538), (805, 538)
    ]
    
    # 总部坐标
    HEADQUARTERS = [
        (364, 907), (526, 907), (-5, 376), (-5, 538), 
        (364, 7), (526, 7), (895, 376), (895, 538)
    ]
    
    # 死亡区域坐标
    AREA_0_DEAD = [(685 + i * 65, 682 + j * 45) for i in range(4) for j in range(6)]
    AREA_1_DEAD = [(670 + i * 45, 217 - j * 65) for j in range(4) for i in range(6)]
    AREA_2_DEAD = [(205 - i * 65, 232 - j * 45) for i in range(4) for j in range(6)]
    AREA_3_DEAD = [(220 - i * 45, 697 + j * 65) for j in range(4) for i in range(6)]
    
    # 路点坐标
    ROAD_POINT = [(445, 727), (364, 772), (526, 772), (445, 817), (283, 907), (364, 907), (445, 907),
                (526, 907), (607, 907), (715, 457), (760, 538), (760, 376), (805, 457), (895, 619),
                (895, 538), (895, 457), (895, 376), (895, 295), (445, 187), (526, 142), (364, 142),
                (445, 97), (607, 7), (526, 7), (445, 7), (364, 7), (283, 7), (175, 457), (130, 376),
                (130, 538), (85, 457), (-5, 295), (-5, 376), (-5, 457), (-5, 538), (-5, 619)]

    # 转向点坐标
    TURN_POINT = [(283, 682), (607, 682), (670, 619), (670, 295), (283, 232), (607, 232), (220, 295), (220, 619)]

    # 棋子原始缩放因子
    PIECE_ORG_SCALE = 55 / 31
    
    @classmethod
    def get_area_list(cls, area_id: int) -> list:
        """获取指定的区域坐标
                
        Args:
            area_id: 区域ID　-> 0-3: 0-3号区域; 4: 中间区域; 5: 军营; 6: 总部; 7: 所有区域
        """
        areas = [cls.AREA_0, cls.AREA_1, cls.AREA_2, cls.AREA_3]
        if 0 <= area_id < 4:
            return areas[area_id]
        elif area_id == 4:
            return cls.AREA_MIDDLE
        elif area_id == 5:
            return cls.BARRACKS
        elif area_id == 6:
            return cls.HEADQUARTERS
        else:
            return cls.ALL_AREAS
    
    @classmethod
    def get_dead_area_list(cls, area_id: int) -> list:
        """获取指定玩家的死亡区域坐标
        
        Args:
            player_id: 死亡区域ID -> 0-3: 0-3号玩家
        """
        dead_areas = [cls.AREA_0_DEAD, cls.AREA_1_DEAD, cls.AREA_2_DEAD, cls.AREA_3_DEAD]
        if 0 <= area_id < 4:
            return dead_areas[area_id]
        return []
    
    @classmethod
    def get_road_point(cls) -> list:
        """获取路点坐标"""
        return cls.ROAD_POINT
    
    @classmethod
    def get_turn_point(cls) -> list:
        """获取转向点坐标"""
        return cls.TURN_POINT
    
    @classmethod
    def get_piece_org_scale(cls) -> float:
        """获取棋子原始缩放因子"""
        return cls.PIECE_ORG_SCALE


class ActiveSign(QGraphicsPixmapItem):
    """活跃标志类 - 实现呼吸透明度循环效果"""

    def __init__(self, image_path=None, parent=None):
        """初始化活跃标志

        Args:
            image_path: 图片文件路径，如果为None则创建空的标志
            parent: 父对象
        """
        super().__init__(parent)

        # 加载图片
        if image_path:
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                self.setPixmap(pixmap)
            else:
                print(f"警告: 无法加载图片文件: {image_path}")
                # 创建一个默认的小圆点作为标志
                self._create_default_pixmap()
        else:
            # 创建默认标志
            self._create_default_pixmap()

        # 初始化动画
        self._setup_breathing_animation()

    def _create_default_pixmap(self):
        """创建默认的活跃标志图片（小圆点）"""
        pixmap = QPixmap(20, 20)
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QBrush(QColor(46, 213, 201)))  # 主题绿色
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(2, 2, 16, 16)
        painter.end()

        self.setPixmap(pixmap)

    def _setup_breathing_animation(self):
        """设置呼吸动画"""
        # 创建一个自定义的透明度属性动画
        self._opacity_value = 1.0

        # 使用定时器来实现呼吸效果
        self.breathing_timer = QTimer()
        self.breathing_timer.timeout.connect(self._update_breathing)
        self.breathing_timer.setInterval(50)  # 50ms更新一次

        # 呼吸参数
        self._breathing_step = 0
        self._breathing_speed = 0.18  # 呼吸速度
        self._min_opacity = 0.3
        self._max_opacity = 1.0

    def _update_breathing(self):
        """更新呼吸效果"""
        import math
        # 使用正弦波来创建平滑的呼吸效果
        self._breathing_step += self._breathing_speed

        # 计算当前透明度值
        opacity_range = self._max_opacity - self._min_opacity
        opacity = self._min_opacity + opacity_range * (math.sin(self._breathing_step) * 0.5 + 0.5)

        # 应用透明度
        self.setOpacity(opacity)

    def start_breathing(self):
        """开始呼吸动画"""
        if not self.breathing_timer.isActive():
            self.breathing_timer.start()
            print("活跃标志开始呼吸动画")

    def stop_breathing(self):
        """停止呼吸动画"""
        if self.breathing_timer.isActive():
            self.breathing_timer.stop()
            self.setOpacity(1.0)  # 恢复完全不透明
            print("活跃标志停止呼吸动画")

    def set_breathing_speed(self, speed: float):
        """设置呼吸速度

        Args:
            speed: 呼吸速度，值越大呼吸越快 (建议范围: 0.01-0.1)
        """
        self._breathing_speed = speed

    def set_opacity_range(self, min_opacity: float, max_opacity: float):
        """设置透明度范围

        Args:
            min_opacity: 最小透明度 (0.0-1.0)
            max_opacity: 最大透明度 (0.0-1.0)
        """
        self._min_opacity = max(0.0, min(1.0, min_opacity))
        self._max_opacity = max(0.0, min(1.0, max_opacity))

        # 确保最小值不大于最大值
        if self._min_opacity > self._max_opacity:
            self._min_opacity, self._max_opacity = self._max_opacity, self._min_opacity


class UIGameCore(QObject):
    """UI游戏核心类
    
    负责处理所有游戏相关的UI逻辑：
    - 游戏事件消息处理
    - 游戏状态管理
    - 游戏UI控制
    """
    
    def __init__(self, main_window, logger):
        """初始化UI游戏核心
        
        Args:
            main_window: 主窗口引用
            logger: 日志记录器
        """
        super().__init__()
        self.main_window = main_window
        self.logger = logger

        self.my_name = None
        self.my_seat = 0
        self.my_last_seat = 0
        self.piece_obj_dic = {}  # 保存所有的棋子对象的字典
        self.pick_phase = False
        self.pieces_picked = []
        self.my_piece = None
        self.enemy_piece = None
        self.rush_lock = 0  # 棋子行动一次后赋值为1，表示被锁定，不可变更选择
        self.active_sign = ActiveSign('./client/image/active_sign.svg')
        
        # 连接游戏相关信号
        self._connect_game_signals()
        
        self.logger.info("UI游戏核心模块初始化完成")
    
    def _connect_game_signals(self):
        """连接游戏相关信号"""
        # 连接游戏事件响应信号
        self.main_window.message_hub.signals.game_event_response.connect(self.handle_game_event)
        self.main_window.message_hub.signals.scene_click_signal.connect(self.handle_scene_click)
        
        self.logger.debug("游戏信号连接完成")
    
    # ==================== 游戏消息处理方法 ====================
    
    def handle_game_event(self, message):
        """处理游戏事件的统一入口"""
        data = message.get("data", {})
        event_type = data.get("type")
        self.logger.info(f"处理游戏事件: {event_type}")
        
        if event_type == "init_game":
            self._handle_game_init(data)
        elif event_type == "init_pieces":
            self.init_pieces(data)
        elif event_type == "pick_pieces":
            self.pick_pieces(data)
        elif event_type == "start_game":
            self.main_window.show_guide_signs(2)
            QTimer.singleShot(1950, lambda: self.start_game(data)) # 延迟1.95秒启动游戏
        elif event_type == "end_game":
            self._handle_game_ended(data)
        else:
            self.logger.warning(f"未知的游戏事件类型: {event_type}")
    
    def _handle_game_init(self, data):
        """处理游戏开始时房间相关事务"""
        self.my_name = self.main_window.user_data['username']
        room_name = data.get("game_room")
        members = data.get("members")
        self.logger.info(f"游戏开始: 房间={room_name}, 成员={members}")

        # 更新房间状态为游戏中
        self.main_window.move_room_to_playing(room_name, members)
        
        # 如果当前用户在游戏中，启动游戏界面
        if self.my_name in members:
            self.my_last_seat = self.my_seat
            self.my_seat = members.index(self.my_name)
            # 同步更新MainWindow的my_seat
            self.main_window.my_seat = self.my_seat
            self.main_window.view.rotate(90 * (self.my_seat - self.my_last_seat))
            print('我转了{}度'.format(90 * (self.my_seat - self.my_last_seat)))
            self.init_game(members)
        elif self.main_window.my_room == room_name:
            self.logger.info(f'{room_name}开始游戏了，无座位之人退出房间')
            self.main_window.my_room = ''
            self.main_window.tabs.removeTab(1)
    
    def _handle_game_ended(self, data):
        """处理游戏结束消息"""
        room_name = data.get("game_room")
        members = data.get("members")
        self.logger.info(f"游戏结束: 房间={room_name}, 成员={members}")
        
        # 更新房间状态为等待中
        self.main_window.move_room_to_waiting(room_name)
        
        # 如果当前用户在游戏中，结束游戏界面
        if self.main_window.user_data['username'] in members:
            self.end_game()


    # ==================== 游戏状态控制方法 ====================

    def init_game(self, members):
        """游戏开始前，进行初始化"""
        self.logger.info("启动游戏界面")
        self.main_window.scene.freeze()
        
        # 更新游戏状态及初始设置
        self.main_window.I_am_playing = True
        self.pick_phase = True
        self.my_piece = None
        self.enemy_piece = None
        self.piece_obj_dic = {}
        self.rush_lock = 0
        items = self.main_window.scene.items()
        for i in items:
            if i != self.main_window.scene.background_svg:  # 排除棋盘对象
                self.main_window.scene.removeItem(i)
                del i
        self.set_name_label(members)


    def end_game(self):
        """游戏结束，显示遮罩"""
        self.logger.info("结束游戏界面")
        # 显示游戏视图遮罩
        self.main_window.view_mask.show()
        # 更新游戏状态
        self.main_window.I_am_playing = False
        # 显示座位选择框架
        self.main_window.seat_choose_frame.show()
        # 显示游戏结束引导标志
        self.main_window.show_guide_signs(3)
        # 将房间移动到等待状态
        self.main_window.move_room_to_waiting(self.main_window.my_room)
    

    # ==================== 棋盘交互方法 ====================

    def init_pieces(self, data):
        """初始化棋子"""
        piece_init_dict = data.get("piece_data")
        self.logger.debug("初始化棋子")
        for i in piece_init_dict:
            Piece(i, piece_init_dict[i], self)  # 实例化棋子
        self.main_window.seat_choose_frame.hide() # 隐藏座位选择框架
        self.main_window.view_mask.hide() # 隐藏游戏视图遮罩
        self.main_window.show_guide_signs(1) # 显示游戏开始引导标志


    def pick_pieces(self, data):
        piece_id = data.get("piece_id")
        position = data.get("position")
        pick_seat = data.get("pick_seat")
        piece = self.piece_obj_dic[piece_id]
        if pick_seat == self.my_seat:
            self.pieces_picked.append(piece)
            print(f'我已经选了{len(self.pieces_picked)}个棋子了',self.pieces_picked)
        piece._update(pick_seat, position, 2, True)


    def start_game(self, data):
        """游戏开始，显示棋子"""
        self.logger.info("游戏开始")
        self.pick_phase = False
        rejust_pos = data.get("rejust_pos")
        first_player = data.get("first_player")
        for i in rejust_pos:
            piece = self.piece_obj_dic[i[0]]
            piece._update(piece.owner, i[1], piece.status)
        if first_player == self.my_seat:
            self.main_window.scene.activate()
        self.show_active_sign(first_player)
        

    def handle_scene_click(self, button, scene_pos):
        """处理棋盘点击事件"""
        item = self.main_window.scene.itemAt(scene_pos, QTransform())
        if self.pick_phase:
            if isinstance(item, Piece) and item.owner != None and (item.owner - self.my_seat) % 2 != 0 and item.status == 1:
                if len(self.pieces_picked) >= 2 or len(self.pieces_picked) == 1 and self.pieces_picked[0].color == item.color:
                    return
                message = {
                    'type': 'game_event',
                    'data': {'type': 'pick_pieces', 'piece_id': item.id, 'position': item.position, 'rank': item.rank, 'owner': item.owner},
                    'user_name': self.my_name,
                    'room_name': self.main_window.my_room
                }
                self.main_window.message_hub.send_to_business_thread(message)
        elif button == 'r':
            if self.my_piece and self.rush_lock == 0:
                print('取消了对我方棋子rank:{} color:{}的选择'.format(self.my_piece.rank, self.my_piece.color))
                self.my_piece = None
        elif isinstance(item, Piece):
            if item.owner == self.my_seat and item.rank != '11' and item.status == 1 and self.rush_lock == 0:
                self.my_piece = item
                print('选择了我方棋子，rank:{} color:{}'.format(item.rank, item.color))
            elif self.my_piece:
                if item.owner is None:
                    print('打算前进到{}处'.format(item.pos))
                    self.enemy_piece = item
                elif item.status == 2 and (item.owner - self.my_seat) % 2 != 0:  # 当是敌方人质棋时
                    print('大本营攻击，rank:{} color:{}'.format(item.rank, item.color))
                    self.enemy_piece = item
                elif item.owner is not None and (item.owner - self.my_seat) % 2 != 0 and item.status == 1:  # 当是敌方棋子时
                    print('我方能攻击到rank:{} color:{}吗？'.format(item.rank, item.color))
                    self.enemy_piece = item
                else:
                    return
                self.navigation(self.my_piece, self.enemy_piece)  # 调用寻路判断方法，看两棋能否按规则接触
                print('选择了目标棋子，准备移动')

    
    def navigation(self, piece1, piece2):  # 从位置角度，判定棋子能否符合规则地到达（攻击）目标位置
        print(f'判断路径中...{piece1.id}->{piece2.id}')

        def go_or_attack(p1: Piece, p2: Piece):
            self.main_window.scene.freeze()
            self.my_piece = None  # 暂时取消己方棋子选择，如服务器判断己方胜利，则由后续方法中再次选定该棋
            self.enemy_piece = None  # 将此轮敌方棋子的选择取消
            self.rush_lock = 1  # 当己方棋子作出行动后，值设为1，只能用该棋进行后续行动（战斗获胜下），不能换选己方其他棋子
            print('我方行动数据交由服务器判断中...')
            message = {
                'type': 'game_event',
                'data': {'type': 'judge_action', 'piece_id_list': [p1.id, p2.id]},
                'user_name': self.my_name,
                'room_name': self.main_window.my_room
            }
            self.main_window.message_hub.send_to_business_thread(message)

        def find_around_pos(pos: tuple):  # 此方法用于找出给定棋位一格范围能到达的所有位置
            plus_pos = []  # 暂存十字线上可能的邻近位置
            cross_pos = []  # 暂存交叉线上可能的邻近位置
            possible_pos = []  # 邻近的所有实际存在的位置
            x = pos[0]
            y = pos[1]
            if pos in GameConfig.get_area_list(4):  # 在中央区域只用考虑　63：中央位与玩家边界位之间一格距离　162：中央位与中央位之间一格距离
                for j in [63, 162]:
                    for k in [-1, 1]:
                        plus_pos.append((x + j * k, y))
                        plus_pos.append((x, y + j * k))
            else:
                for j in [45, 63, 81]:  # 在玩家区域只用考虑　63：中央位与玩家边界位之间距离　45、81：上下、左右玩家区域不同的相邻棋位距离
                    for k in [-1, 1]:
                        plus_pos.append((x + j * k, y))
                        plus_pos.append((x, y + j * k))
            possible_pos = list(set(plus_pos) & set(GameConfig.get_area_list(7))) # 理论上的位置与实际位置作交集，来确定十字方向上相邻位
            for j in [x + 45, x - 45]:
                for k in [y + 81, y - 81]:
                    cross_pos.append((j, k))  # 然后再找出给定棋位斜角位的位置
            for j in [x + 81, x - 81]:
                for k in [y + 45, y - 45]:
                    cross_pos.append((j, k))
            if pos in GameConfig.get_area_list(5):
                possible_pos += list(set(cross_pos) & set(GameConfig.get_area_list(7)))  # 在军营中的棋子可确定到达四个斜角位
            else:
                cross_pos += [(x + 63, y + 63), (x + 63, y - 63), (x - 63, y + 63), (x - 63, y - 63)]  # 玩家边界相邻斜角位的距离
                for j in cross_pos:
                    if j in GameConfig.get_area_list(5) + GameConfig.get_turn_point():  # 不在军营中的棋子，只有斜角位是军营或对手边界位时，才有可能到达
                        possible_pos.append(j)
            print('{}周围所有的位置：{}'.format(pos,possible_pos))
            return possible_pos  # 返回所有可能到达的位置信息，以作进一步判定

        def make_vector(origin_nod_pos: tuple, origin_nod_info: list, around_list: list):  # 此方法用遍历来查找、判断棋子能否一步步到达目标位
            print('攻击者在此坐标：{}{} 后续可达位置：{}　目标：{}'.format(origin_nod_pos, origin_nod_info, around_list, piece2.position))
            
            piece_exist_pos = []
            exclude_pos = []
            for target_pos in around_list:
                target_piece = self.main_window.scene.itemAt((target_pos[0] * self.main_window.scale + 31 * GameConfig.get_piece_org_scale() * self.main_window.scale / 2),
                     (target_pos[1] * self.main_window.scale + 17 * GameConfig.get_piece_org_scale() * self.main_window.scale / 2), QTransform())
                if target_piece.owner is not None:  # 把已经存在棋子的位置收集起来
                    piece_exist_pos.append(target_pos)

                # 如果目标位置在军营中，且不是空位，无法攻击, 中断任务
                if target_pos in GameConfig.get_area_list(5) and target_piece.owner is not None:
                    print(f'{target_piece.id}目标在军营中，且不是空位，无法攻击')

                # 如果目标在军营或公路点，且不是第一步行动，无法攻击，中断任务
                elif (target_pos in GameConfig.get_area_list(5) or target_pos in GameConfig.get_road_point()) and origin_nod_info[0] is not None:
                    print(f'{target_piece.id}目标在军营或公路点，且不是第一步行动，无法攻击')

                # 如果目标在军营空位或公路点，可以到达（但需要后续处理）
                elif (target_pos in GameConfig.get_area_list(5) and target_piece.owner is None) or target_pos in GameConfig.get_road_point():  # 可到达军营空位或公路点
                    target_dict[target_pos] = [3, 3] # 加入vector_dic中, 后续统一判断是否是最终目标位置，不是的话要排除此位置才能作为后续参数
                    exclude_pos.append(target_pos)  # 同时把此位置加入排除项中
                    print(f'可以到达军营空位或公路点{target_pos}，但不作为后续出发节点')

                # 发起点在军营或公路点（但需要后续处理）
                elif origin_nod_pos in GameConfig.get_area_list(5) or origin_nod_pos in GameConfig.get_road_point():
                    print(f'{piece1.id}发起点在军营或公路点，可以到达{target_pos}，但不作为后续出发节点')
                    target_dict[target_pos] = [3, 3] # 加入vector_dic中, 后续统一判断是否是最终目标位置，不是的话要排除此位置才能作为后续参数
                    exclude_pos.append(target_pos)  # 同时把此位置加入排除项中

                # 如果目标在转角点，且发起点也在转角点
                elif target_pos in GameConfig.get_turn_point() and origin_nod_pos in GameConfig.get_turn_point():  # 特殊情况1，边界转弯时切换锁定方向
                    print(f'{origin_nod_pos}转角到转角{target_pos}')
                    if target_pos in (GameConfig.get_area_list(0) + GameConfig.get_area_list(2)):
                        if origin_nod_info[0] == 1 or origin_nod_info[0] is None:
                            target_dict[target_pos] = [0, 1]  # 以位置为键，值的第一位0为X值锁定，1为Y值锁定，2为不锁定；第二位0为不许转弯，1为转弯额度剩1
                    elif target_pos in (GameConfig.get_area_list(1) + GameConfig.get_area_list(3)):
                        if origin_nod_info[0] == 0 or origin_nod_info[0] is None:
                            target_dict[target_pos] = [1, 1]

                # 行动发起点在中央区域时
                elif origin_nod_pos in GameConfig.get_area_list(4):
                    print('在中央位置{}开始探路'.format(origin_nod_pos))
                    if target_dict.get(target_pos) is not None and origin_nod_info[1] != 0:  # 发起点出发的两条线路转弯后的交汇点，成为双向发起点
                        target_dict[target_pos][0] = 2  # 在字典查找该位置的键，有则代表已经有线路经过，修改相应的值为作标记
                        print('发现下一轮中的交会点{}'.format(target_pos))
                    elif origin_nod_info[0] == 2:  # 双向发起点，可按x,y方向到达相邻位，并再次作好方向锁定
                        if target_pos[0] == origin_nod_pos[0]:
                            target_dict[target_pos] = [0, 0]
                        target_dict[target_pos] = [1, 0]
                        print('由交会点{}出发,达到其中一个位置{}'.format(origin_nod_pos, target_pos))
                    elif origin_nod_info[0] is None:  # 第一步出发，没有锁定方向，可以全向到达
                        if target_pos[0] == origin_nod_pos[0]:  # 与发起点x值相同时，锁定x方向
                            target_dict[target_pos] = [0, 1]
                        elif target_pos[1] == origin_nod_pos[1]:  # 与发起点y值相同时，锁定y方向
                            target_dict[target_pos] = [1, 1]
                        print('由中央发起点{}初始出发,达到其中一个位置{}'.format(origin_nod_pos, target_pos))
                    elif target_pos[origin_nod_info[0]] != origin_nod_pos[origin_nod_info[0]] and origin_nod_info[1] == 1:
                        target_dict[target_pos] = [abs(origin_nod_info[0] - 1), 0]  # 有转弯额度，相邻点不在锁定方向上也可到达，但要切换锁定方向，额度清零
                        print('{}不在{}{}的锁定路径上，但消耗点数转弯一次'.format(target_pos, target_dict[target_pos], origin_nod_pos, origin_nod_info))
                    elif target_pos[origin_nod_info[0]] == origin_nod_pos[origin_nod_info[0]]:  # 相邻点在锁定方向上，直接传递锁定方向和转弯额度
                        target_dict[target_pos] = [origin_nod_info[0], origin_nod_info[1]]
                        print('{}{}在{}{}的锁定路径上'.format(target_pos, target_dict[target_pos], origin_nod_pos, origin_nod_info))
                    else:
                        print('中间区域经过{}位置发起的攻击无法触及到{}'.format(origin_nod_pos, target_pos))
                        continue

                elif origin_nod_info[0] is None:  # 行动发起点的第一步，可以全向到达（攻击）每个相邻位置（排除军营中的棋子）
                    if target_pos[0] == origin_nod_pos[0]:  # 与发起点x值相同时，锁定x方向
                        target_dict[target_pos] = [0, 1]
                    elif target_pos[1] == origin_nod_pos[1]:  # 与发起点y值相同时，锁定y方向
                        target_dict[target_pos] = [1, 1]
                    print('由非中央区域发起点{}出发的第一步,达到其中一个位置{}'.format(origin_nod_pos, target_pos))

                elif target_pos[origin_nod_info[0]] == origin_nod_pos[origin_nod_info[0]]:  # 相邻点是在铁路上，且在x或y锁定上时，可以到达（攻击）
                    target_dict[target_pos] = [origin_nod_info[0], origin_nod_info[1]]
                    print(f'{target_pos}在铁路上，且在锁定方向上，可以到达')

                else:  # 其他情况都不行
                    print(f'{target_pos}此位置无法触及，判断下一个位置')
                    continue

                print(f'已将一个位置{target_pos}加入target_dic中, 继续判断下一个位置')

            print(f'此路径现在可攻击位置: {target_dict}')
            if piece2.position in target_dict:  # 如果目标位置在本轮可触及的范围内，就结束查找
                print(f'找到目标棋子{piece2.id}')
                go_or_attack(piece1, piece2)
                return 1

            for i in (exclude_pos + piece_exist_pos):
                if i in target_dict:
                    del target_dict[i]
            print(f'目前下一轮寻路出发节点：{target_dict}')  # 同时排除上一轮的行动发起位置、特定排除位置以及有棋存在的位置

            '''工兵特殊处理'''
            # if piece1.rank == '01':  # 工兵的特性，不必对每个位置作方向的判定，直接用under_watch中筛选的位置作成字典进入下一轮查找
            #     for j in under_watch:
            #         vector_dic[j] = [0, 0]
            #     return


        next_nod_dict = {piece1.position: [None, 1]}  # 初始字典值设为空值列表
        target_dict = {}  # 储存一轮过程中生成的字典项，每一项包含了下一步查找的位置、方向、转弯信息
        last_nod_pos = set()  # 把一轮用过的位置存入其中，每轮新位置集合要排除这里的位置信息
        while True:
            break_flag = 0  # 设置一个打断循环标志
            for i in next_nod_dict:
                if i not in last_nod_pos: # 此条件判断在于去除中央区域到turn_point点后，再拐弯到另一个turn_point点的无效路径
                    around_pos = list(set(find_around_pos(i)) - last_nod_pos)
                    r = make_vector(i, next_nod_dict[i], around_pos)
                    if r == 1:  # 接收到完成查找的返回值，就改变标志值
                        break_flag = 1
                        break
            if break_flag == 1 or not target_dict:  # 每轮循环检查打断标志，为1则打断
                break
            last_nod_pos = set(next_nod_dict.keys())  # 把上一轮所有用过的位置存入
            next_nod_dict = target_dict  # 接收上一轮的vector_dic信息，作为下一轮循环的参数
            target_dict = {}  # 清空，准备接收下一轮查找到的有用信息
        print('跳出')


    def _truncate_name(self, name, max_length=6):
        """截断名字，汉字计2，ASCII字符计1，总长度不超过max_length"""
        if not name:
            return ""

        current_length = 0
        result = ""

        for char in name:
            # 判断是否为汉字（简单判断：Unicode编码范围）
            if '\u4e00' <= char <= '\u9fff':
                char_length = 2  # 汉字计为2
            else:
                char_length = 1  # ASCII字符计为1

            if current_length + char_length <= max_length:
                result += char
                current_length += char_length
            else:
                break

        return result

    def set_name_label(self, player_list):
        """设置玩家名称标签"""
        self.scale = self.main_window.scale
        for i in range(4):
            label = QGraphicsSimpleTextItem()
            label.setBrush(QColor("#553810"))  # 使用自定义棕色
            # 截断名字长度
            truncated_name = self._truncate_name(player_list[i])
            label.setText(truncated_name)
            label.setFont(QFont("黑体", 14 * self.scale))
            self.main_window.scene.addItem(label)
            label.setTransformOriginPoint(60 * self.scale / 2, 20 * self.scale / 2)
            if i == 0:
                label.setPos(375 * self.scale, 655 * self.scale)
                label.setRotation(0)
            elif i == 1:
                label.setPos(635 * self.scale, 530 * self.scale)
                label.setRotation(-90)
            elif i == 2:
                label.setPos(508 * self.scale, 270 * self.scale)
                label.setRotation(180)
            else:
                label.setPos(250 * self.scale, 395 * self.scale)
                label.setRotation(90)

    def show_active_sign(self, code):
        self.pic_pos = [(439.5, 538), (543, 434.5), (439.5, 331), (336, 434.5)]
        self.main_window.scene.addItem(self.active_sign)
        self.active_sign.setTransformOriginPoint(65 / 2, 75 / 2)
        self.active_sign.setRotation(-90 * code)
        self.active_sign.setScale(self.scale * 1.2)
        print('放置并旋转到{}处'.format(code))
        self.active_sign.setPos(self.pic_pos[code][0] * self.scale, self.pic_pos[code][1] * self.scale)
        self.active_sign.start_breathing()
        print('放好了')


    # ==================== 清理方法 ====================
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("清理UI游戏核心资源")
        
        # 断开信号连接
        try:
            self.main_window.message_hub.signals.game_event_response.disconnect(self.handle_game_event)
        except Exception as e:
            self.logger.warning(f"断开游戏信号连接时出错: {e}")
        
        self.logger.info("UI游戏核心资源清理完成")



class Piece(QGraphicsSvgItem):  # 棋子类，继承于QGraphicsSvgItem类，可绑定矢量图片
    def __init__(self, id, data_list, game_core):
        super(Piece, self).__init__()
        # self.setOpacity(0.5)
        self.id = id
        self.owner = data_list[0]
        self.color = data_list[1]
        self.rank = data_list[2]
        self.position = tuple(data_list[3])
        self.status = data_list[4]  # 状态判断标志位，0：死亡　1：存活　2：人质
        self.visible = True  # 调试时要显示全部棋子时，设为True
        if self.owner == game_core.main_window.my_seat:
            self.setCursor(Qt.CursorShape.PointingHandCursor)
            self.visible = True
        self.game_core = game_core
        self.setTransformOriginPoint(31 / 2, 17 / 2)  # 变换基准点处于物体自身坐标系，影响一切变形效果，包括缩放和旋转
        self.setScale(GameConfig.get_piece_org_scale())  # 先变换为初始大小
        self.setScale(GameConfig.get_piece_org_scale() * game_core.main_window.scale)  # 再变换为所需大小
        self.game_core.piece_obj_dic[id] = self
        self.game_core.main_window.scene.addItem(self)  # 将棋子对象置入场景，才能显示出来
        self.layout()

    def _update(self, owner, position, status, visible='remain'):  # 更新棋子信息
        '''
        args:
            data_list: [owner, position, status -- 0:死亡 1:存活 2:人质]
        '''
        self.owner = owner
        self.position = tuple(position)
        self.status = status  # 状态判断标志位，0：死亡　1：存活　2：人质
        if status != 1:
            self.setCursor(Qt.CursorShape.ArrowCursor)
        if not visible == 'remain':
            self.visible = visible
        self.layout()

    def layout(self):
        self.pic_update()
        self.setPos((self.position[0] * self.game_core.main_window.scale + 31 * (GameConfig.get_piece_org_scale() * self.game_core.main_window.scale - 1) / 2),
                     (self.position[1] * self.game_core.main_window.scale + 17 * (GameConfig.get_piece_org_scale() * self.game_core.main_window.scale - 1) / 2))
        self.rotate()

    def pic_update(self):
        if self.owner is None:
            pic_name = '.\\client\\image\\pieces\\blank.svg'
            self.setOpacity(0.5)
        elif self.visible:
            pic_name = '.\\client\\image\\pieces\\' + self.color + '\\' + self.rank + '.svg'
        else:
            pic_name = '.\\client\\image\\pieces\\' + self.color + '\\' + 'back.svg'
        self.render = QSvgRenderer()
        if self.render.load(pic_name):
            self.setSharedRenderer(self.render)
        else:
            print(f"无法加载SVG文件: {pic_name}")

    def rotate(self):  # 判断棋子的阵营及所处位置，执行正确地旋转
        pos = self.position
        center_area = GameConfig.get_area_list(4)
        if self.owner is None:  # 对空白棋的旋转策略
            if pos in center_area or pos in self.which_area(self.game_core.my_seat, 0) or pos in self.which_area(self.game_core.my_seat, 1):
                self.setRotation(-90 * self.game_core.my_seat)  # 在自身、友方区域及中央区域按己方棋子处理
            elif pos in self.which_area(self.game_core.my_seat, 2):
                self.setRotation(-90 * (self.game_core.my_seat - 1))  # 在敌方区域，空白棋子相对己方棋子少转90度
        elif pos in center_area or pos in self.which_area(self.owner, 0) or pos in self.which_area(self.owner, 1):
            self.setRotation(-90 * self.owner)  # 各方棋子在自身区域、中央区域，正常旋转
        elif pos in self.which_area((lambda x: x + 1 if x < 3 else 0)(self.owner), 0):
            self.setRotation(-90 * (self.owner - 1))  # 棋子在右手侧敌人区域时，按左手侧敌人的角度旋转
        elif pos in self.which_area((lambda x: x - 1 if x > 0 else 3)(self.owner), 0):
            self.setRotation(-90 * (self.owner + 1))  # 棋子在左手侧敌人区域时，按右手侧敌人的角度旋转
        else:
            self.setRotation(-90 * self.owner)  # 其他情况，正常旋转

    def which_area(self, owner: int, num: int) -> list:
        '''
        args:
            owner: 棋子所属玩家
            num: 传入的num值决定返回列表　0:己方区域　1：友方区域　2：敌方区域
        '''
        # 确保 owner 在有效范围内
        if not (0 <= owner <= 3):
            return []

        dic = {0: GameConfig.get_area_list(0) + GameConfig.get_dead_area_list(0),
               1: GameConfig.get_area_list(1) + GameConfig.get_dead_area_list(1),
               2: GameConfig.get_area_list(2) + GameConfig.get_dead_area_list(2),
               3: GameConfig.get_area_list(3) + GameConfig.get_dead_area_list(3)
        }
        if num == 0:  # 调用者请求返回code方的自身区域
            return dic[owner]
        elif num == 1:  # 调用者请求返回code方的友方区域
            if owner < 2:
                return dic[owner + 2]
            else:
                return dic[owner - 2]
        elif num == 2:  # 调用者请求返回code方的敌方区域
            if 0 < owner < 3:
                return dic[owner + 1] + dic[owner - 1]
            elif owner == 0:
                return dic[1] + dic[3]
            else:  # owner == 3
                return dic[0] + dic[2]
        else:
            # 对于其他 num 值，返回空列表
            return []


    def __del__(self):  # 察看棋子对象是否被删除，释放内存
        print('del', self.id)

