"""
工具包模块
包含各种通用的工具类和函数
"""

import asyncio
import threading
from typing import Optional, Dict, Any, List
import logging
import logging.handlers
import os
from datetime import datetime
import inspect
import re


class LogConfig:
    """日志配置类"""
    # 性能模式：只记录性能相关日志到文件
    PERFORMANCE_MODE = False

    # 详细模式：记录所有日志
    VERBOSE_MODE = True

    @classmethod
    def set_performance_mode(cls, enabled: bool):
        """设置性能模式"""
        cls.PERFORMANCE_MODE = enabled
        cls.VERBOSE_MODE = not enabled

    @classmethod
    def set_verbose_mode(cls, enabled: bool):
        """设置详细模式"""
        cls.VERBOSE_MODE = enabled
        if enabled:
            cls.PERFORMANCE_MODE = False


class PerformanceLogger:
    """性能分析日志记录器

    专门用于记录通信时间和性能相关的日志，便于压力测试分析。
    只记录关键的时间节点，减少日志噪音。
    """
    _instance = None
    _lock = threading.Lock()
    log_dir: str = 'server/logs'

    def __new__(cls, log_dir: str = ''):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                if log_dir:
                    cls._instance.log_dir = log_dir
                cls._instance.setup_performance_logger()
            return cls._instance

    def setup_performance_logger(self):
        """设置性能日志记录器"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        current_time = datetime.now().strftime('%Y%m%d')
        perf_log_file = os.path.join(self.log_dir, f'performance_{current_time}.log')

        self.perf_logger = logging.getLogger('PerformanceLogger')
        self.perf_logger.setLevel(logging.INFO)

        # 创建性能日志文件处理器
        self.perf_file_handler = logging.handlers.TimedRotatingFileHandler(
            perf_log_file,
            when='midnight',
            interval=1,
            backupCount=30,  # 保留30天的性能日志
            encoding='utf-8'
        )
        self.perf_file_handler.setLevel(logging.INFO)

        # 设置简化的性能日志格式（便于分析）
        perf_formatter = logging.Formatter(
            '%(asctime)s.%(msecs)03d|%(threadName)s|%(message)s',
            datefmt='%H:%M:%S'
        )
        self.perf_file_handler.setFormatter(perf_formatter)

        # 确保处理器不会重复添加
        self.perf_logger.handlers = []
        self.perf_logger.addHandler(self.perf_file_handler)
        self.perf_logger.propagate = False  # 不传播到根日志器

    def log_timing(self, event: str, socket_id: str = "", message_type: str = ""):
        """记录时间相关事件

        Args:
            event: 事件名称 (如: NET_RECV, HUB_RECV, DC_PROC, HUB_RESP, NET_SEND)
            socket_id: 连接ID
            message_type: 消息类型
        """
        msg = f"{event}|{socket_id}|{message_type}"
        self.perf_logger.info(msg)

    def cleanup_performance(self):
        """清理性能日志系统"""
        try:
            handlers = self.perf_logger.handlers[:]
            for handler in handlers:
                handler.close()
                self.perf_logger.removeHandler(handler)
            PerformanceLogger._instance = None
        except Exception as e:
            print(f"清理性能日志系统时发生错误: {e}")


class Logger:
    """日志记录器工具类

    单例模式的日志记录器，提供统一的日志记录接口。
    支持文件和控制台双重输出，自动按日期轮转日志文件。
    """
    _instance = None
    _lock = threading.Lock()
    log_dir: str = 'server/logs'

    # 声明实例属性
    logger: Optional[logging.Logger]
    file_handler: Optional[logging.handlers.TimedRotatingFileHandler]
    console_handler: Optional[logging.StreamHandler]
    perf_logger: Optional['PerformanceLogger']

    def __new__(cls, log_dir: str = '', logger_name: str = ''):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                # 初始化实例属性
                cls._instance.logger = None
                cls._instance.file_handler = None
                cls._instance.console_handler = None
                cls._instance.perf_logger = None
                # 设置日志目录
                if log_dir:
                    cls._instance.log_dir = log_dir
                # 设置日志器
                cls._instance.setup_logger(logger_name)
                # 初始化性能日志器
                cls._instance.perf_logger = PerformanceLogger(log_dir)
            return cls._instance

    def setup_logger(self, logger_name: str = ''):
        """设置日志记录器"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        current_time = datetime.now().strftime('%Y%m%d')
        # 根据日志目录自动确定日志文件前缀
        if 'client' in self.log_dir:
            log_prefix = 'client'
            default_logger_name = 'ClientLogger'
        else:
            log_prefix = 'server'
            default_logger_name = 'ServerLogger'

        log_file = os.path.join(self.log_dir, f'{log_prefix}_{current_time}.log')

        self.logger = logging.getLogger(logger_name or default_logger_name)
        self.logger.setLevel(logging.DEBUG)

        # 创建TimedRotatingFileHandler
        self.file_handler = logging.handlers.TimedRotatingFileHandler(
            log_file,
            when='midnight',
            interval=1,
            backupCount=15,  # 保留15天的日志
            encoding='utf-8'
        )
        # 禁用缓存，立即写入（调试用）
        def force_flush():
            try:
                if self.file_handler and hasattr(self.file_handler, 'stream'):
                    stream = getattr(self.file_handler, 'stream', None)
                    if stream and hasattr(stream, 'flush'):
                        stream.flush()
            except Exception:
                pass  # 忽略flush错误
        self.file_handler.flush = force_flush

        # 根据配置设置文件日志级别
        if LogConfig.PERFORMANCE_MODE:
            self.file_handler.setLevel(logging.CRITICAL)  # 性能模式下文件几乎不记录
        else:
            self.file_handler.setLevel(logging.DEBUG)

        # 设置控制台显示日志级别
        self.console_handler = logging.StreamHandler()
        self.console_handler.setLevel(logging.INFO)  # 控制台只显示INFO及以上级别

        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(threadName)s - %(module)s:%(lineno)d - %(message)s'
        )
        self.file_handler.setFormatter(formatter)
        self.console_handler.setFormatter(formatter)

        # 确保处理器不会重复添加
        self.logger.handlers = []
        self.logger.addHandler(self.file_handler)
        self.logger.addHandler(self.console_handler)

        # 添加启动日志
        self.logger.info('日志记录器初始化完成')

    def _get_caller_info(self):
        """获取调用者信息"""
        # 获取当前帧
        current_frame = inspect.currentframe()
        try:
            # 获取调用栈
            frames = inspect.getouterframes(current_frame)
            # 跳过内部方法调用，找到实际的调用者
            # 0: current frame (_get_caller_info)
            # 1: _log
            # 2: debug/info/warning/error/critical
            # 3: actual caller
            caller_frame = frames[3]
            return caller_frame.filename, caller_frame.lineno
        finally:
            del current_frame  # 清理引用，避免内存泄漏

    def _log(self, level: int, msg: str):
        """同步日志处理"""
        if not self.logger:
            print(f"Logger not initialized: {msg}")
            return

        pathname, lineno = self._get_caller_info()
        record = logging.LogRecord(
            name=self.logger.name,
            level=level,
            pathname=pathname,
            lineno=lineno,
            msg=msg,
            args=(),
            exc_info=None
        )
        try:
            self.logger.handle(record)
        except Exception as e:
            print(f"日志处理错误: {e}")

    def debug(self, msg: str):
        """记录debug级别日志"""
        self._log(logging.DEBUG, msg)

    def info(self, msg: str):
        """记录info级别日志"""
        self._log(logging.INFO, msg)

    def warning(self, msg: str):
        """记录warning级别日志"""
        self._log(logging.WARNING, msg)

    def error(self, msg: str):
        """记录error级别日志"""
        self._log(logging.ERROR, msg)

    def critical(self, msg: str):
        """记录critical级别日志"""
        self._log(logging.CRITICAL, msg)

    def timing(self, event: str, socket_id: str = "", message_type: str = ""):
        """记录性能时间事件

        Args:
            event: 事件名称 (NET_RECV, HUB_RECV, DC_PROC, HUB_RESP, NET_SEND)
            socket_id: 连接ID
            message_type: 消息类型
        """
        if self.perf_logger and hasattr(self.perf_logger, 'log_timing'):
            self.perf_logger.log_timing(event, socket_id, message_type)

    def cleanup(self):
        """清理日志系统"""
        try:
            # 记录最后一条日志
            self._log(logging.INFO, '日志系统关闭')

            # 清理性能日志
            if self.perf_logger and hasattr(self.perf_logger, 'cleanup_performance'):
                self.perf_logger.cleanup_performance()

            # 关闭所有处理器
            if self.logger and hasattr(self.logger, 'handlers'):
                handlers = self.logger.handlers[:]
                for handler in handlers:
                    handler.close()
                    self.logger.removeHandler(handler)

            # 重置单例实例
            Logger._instance = None

        except Exception as e:
            print(f"清理日志系统时发生错误: {e}")


def get_logger(name: str = '', log_dir: str = '') -> Logger:
    """获取Logger实例的便捷函数

    Args:
        name: 日志器名称，可选
        log_dir: 日志目录，可选

    Returns:
        Logger实例
    """
    return Logger(log_dir, name)


class ThreadedEventLoop:
    """线程化事件循环管理器
    
    用于在独立线程中运行异步事件循环，提供线程安全的任务提交接口。
    适用于需要在多线程环境中管理异步任务的场景。
    """
    
    def __init__(self, name: str, logger: Logger):
        """初始化线程事件循环管理器
        
        Args:
            name: 线程名称，用于日志标识
            logger: 日志记录器实例
        """
        self.name = name
        self.logger = logger
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self._thread: Optional[threading.Thread] = None
        self._running = False
        self._tasks = set()
        self.logger.info(f"创建线程事件循环管理器: {name}")

    def start(self):
        """启动线程和事件循环"""
        if self._running:
            self.logger.warning(f"线程 {self.name} 已在运行，跳过启动")
            return

        self.logger.info(f"正在启动线程: {self.name}")
        self._running = True
        self._thread = threading.Thread(target=self._run_event_loop, name=self.name)
        self._thread.daemon = True
        self._thread.start()
        self.logger.info(f"线程 {self.name} 启动成功")

    def _run_event_loop(self):
        """在线程中运行事件循环"""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.logger.info(f"线程 {self.name} 的事件循环已启动")
            self.loop.run_forever()
            self.logger.info(f"线程 {self.name} 的事件循环已停止，开始清理")
        except Exception as e:
            self.logger.error(f"线程 {self.name} 事件循环运行异常: {e}")
        finally:
            # 统一在这里处理所有清理工作
            if self.loop:
                pending = asyncio.all_tasks(self.loop)
                if pending:
                    self.logger.info(f"线程 {self.name} 正在取消 {len(pending)} 个未完成的任务")
                    for task in pending:
                        task.cancel()
                    # 等待所有任务完成取消
                    try:
                        self.loop.run_until_complete(
                            asyncio.gather(*pending, return_exceptions=True)
                        )
                        self.logger.info(f"线程 {self.name} 所有任务已取消完成")
                    except Exception as e:
                        self.logger.error(f"线程 {self.name} 取消任务时出错: {e}")
                else:
                    self.logger.info(f"线程 {self.name} 没有待清理的任务")

                self.loop.close()
                self.logger.info(f"线程 {self.name} 事件循环已关闭")
                self.loop = None

    def stop(self):
        """停止事件循环和线程"""
        if not self._running:
            self.logger.warning(f"线程 {self.name} 已停止，跳过停止操作")
            return

        self.logger.info(f"正在停止线程: {self.name}")
        self._running = False
        if self.loop is not None:
            # 确保在正确的线程中停止事件循环
            self.logger.debug(f"向线程 {self.name} 发送停止信号")
            self.loop.call_soon_threadsafe(self._stop_loop)
        if self._thread is not None:
            self.logger.debug(f"等待线程 {self.name} 结束")
            self._thread.join(timeout=10.0)  # 添加超时避免无限等待
            if self._thread.is_alive():
                self.logger.error(f"线程 {self.name} 停止超时")
            else:
                self.logger.info(f"线程 {self.name} 已成功停止")
            
    def _stop_loop(self):
        """在事件循环线程中停止循环"""
        if self.loop is not None:
            self.logger.debug(f"线程 {self.name} 正在停止事件循环")
            # 只停止循环，不取消任务
            self.loop.stop()
        else:
            self.logger.warning(f"线程 {self.name} 的事件循环已为空，无需停止")

    def submit_task(self, coro):
        """提交异步任务到事件循环

        Args:
            coro: 要执行的协程对象

        Returns:
            Future对象，可用于获取任务结果，失败时返回None
        """
        if self.loop is not None and self._running:
            self.logger.debug(f"向线程 {self.name} 提交任务: {coro.__name__ if hasattr(coro, '__name__') else str(coro)}")
            try:
                future = asyncio.run_coroutine_threadsafe(coro, self.loop)
                self._tasks.add(future)
                return future
            except Exception as e:
                self.logger.error(f"向线程 {self.name} 提交任务失败: {e}")
                return None
        else:
            self.logger.warning(f"线程 {self.name} 未运行或事件循环不可用，无法提交任务")
            return None


class PasswordPreValidator:
    """密码验证器工具类

    全局单例模式的密码验证器，提供统一的密码格式检验功能。
    支持多种密码强度级别和自定义验证规则。
    适用于创建密码、输入密码时的预先格式检验等场景。
    """
    _instance = None
    _lock = threading.Lock()

    # 预定义的密码强度级别
    STRENGTH_WEAK = "weak"
    STRENGTH_MEDIUM = "medium"
    STRENGTH_STRONG = "strong"
    STRENGTH_VERY_STRONG = "very_strong"

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialize()
            return cls._instance

    def _initialize(self):
        """初始化密码验证器配置"""
        # 默认配置
        self._config = {
            # 基础长度要求
            'min_length': 8,
            'max_length': 128,

            # 字符类型要求
            'require_lowercase': True,      # 需要小写字母
            'require_uppercase': False,     # 需要大写字母
            'require_digits': True,         # 需要数字
            'require_special': False,       # 需要特殊字符

            # 特殊字符定义
            'special_chars': '!@#$%^&*()_+-=[]{}|;:,.<>?',

            # 禁止的模式
            'forbidden_patterns': [
                r'(.)\1{2,}',              # 连续相同字符（3个或以上）
                r'(012|123|234|345|456|567|678|789|890)',  # 连续数字
                r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)',  # 连续字母
            ],

            # 常见弱密码列表
            'common_passwords': [
                'password', '123456', '123456789', 'qwerty', 'abc123',
                'password123', 'admin', 'root', 'user', 'guest',
                '111111', '000000', '12345678', 'qwerty123'
            ]
        }

        # 强度级别配置
        self._strength_configs = {
            self.STRENGTH_WEAK: {
                'min_length': 6,
                'require_lowercase': True,
                'require_uppercase': False,
                'require_digits': False,
                'require_special': False,
            },
            self.STRENGTH_MEDIUM: {
                'min_length': 8,
                'require_lowercase': True,
                'require_uppercase': False,
                'require_digits': True,
                'require_special': False,
            },
            self.STRENGTH_STRONG: {
                'min_length': 10,
                'require_lowercase': True,
                'require_uppercase': True,
                'require_digits': True,
                'require_special': False,
            },
            self.STRENGTH_VERY_STRONG: {
                'min_length': 12,
                'require_lowercase': True,
                'require_uppercase': True,
                'require_digits': True,
                'require_special': True,
            }
        }

    def set_strength_level(self, strength: str) -> bool:
        """设置密码强度级别

        Args:
            strength: 强度级别 (weak/medium/strong/very_strong)

        Returns:
            bool: 设置成功返回True，无效级别返回False
        """
        if strength not in self._strength_configs:
            return False

        # 更新当前配置
        self._config.update(self._strength_configs[strength])
        return True

    async def validate_password(self, password: str, strength: str = '') -> Dict[str, Any]:
        """异步验证密码是否符合规范

        Args:
            password: 待验证的密码明文
            strength: 可选的强度级别，如果提供则临时使用该级别验证

        Returns:
            Dict: 验证结果
            {
                'is_valid': bool,           # 是否通过验证
                'score': int,               # 密码强度评分 (0-100)
                'errors': List[str],        # 错误信息列表
                'warnings': List[str],      # 警告信息列表
                'suggestions': List[str]    # 改进建议列表
            }
        """
        result = {
            'is_valid': True,
            'score': 0,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }

        # 如果指定了临时强度级别，使用临时配置
        config = self._config.copy()
        if strength and strength in self._strength_configs:
            config.update(self._strength_configs[strength])

        # 基础检查
        if not password:
            result['errors'].append('密码不能为空')
            result['is_valid'] = False
            return result

        # 异步yield点 - 让出控制权，避免阻塞事件循环
        await asyncio.sleep(0)

        # 长度检查
        length = len(password)
        if length < config['min_length']:
            result['errors'].append(f'密码长度不能少于{config["min_length"]}位')
            result['is_valid'] = False
        elif length > config['max_length']:
            result['errors'].append(f'密码长度不能超过{config["max_length"]}位')
            result['is_valid'] = False
        else:
            # 长度评分 (最多30分)
            result['score'] += min(30, (length / config['max_length']) * 30)

        # 异步yield点 - 在复杂计算前让出控制权
        await asyncio.sleep(0)

        # 字符类型检查
        has_lowercase = bool(re.search(r'[a-z]', password))
        has_uppercase = bool(re.search(r'[A-Z]', password))
        has_digits = bool(re.search(r'[0-9]', password))
        has_special = bool(re.search(f'[{re.escape(config["special_chars"])}]', password))

        # 必需字符类型检查
        if config['require_lowercase'] and not has_lowercase:
            result['errors'].append('密码必须包含小写字母')
            result['is_valid'] = False

        if config['require_uppercase'] and not has_uppercase:
            result['errors'].append('密码必须包含大写字母')
            result['is_valid'] = False

        if config['require_digits'] and not has_digits:
            result['errors'].append('密码必须包含数字')
            result['is_valid'] = False

        if config['require_special'] and not has_special:
            result['errors'].append(f'密码必须包含特殊字符: {config["special_chars"]}')
            result['is_valid'] = False

        # 字符多样性评分 (最多40分)
        char_types = sum([has_lowercase, has_uppercase, has_digits, has_special])
        result['score'] += char_types * 10

        # 异步yield点 - 在模式检查前让出控制权
        await asyncio.sleep(0)

        # 禁止模式检查
        for pattern in config['forbidden_patterns']:
            if re.search(pattern, password, re.IGNORECASE):
                result['errors'].append('密码包含禁止的模式（如连续字符、连续数字等）')
                result['is_valid'] = False
                break

        # 异步yield点 - 在弱密码检查前让出控制权
        await asyncio.sleep(0)

        # 常见弱密码检查
        if password.lower() in [pwd.lower() for pwd in config['common_passwords']]:
            result['errors'].append('密码过于常见，请使用更复杂的密码')
            result['is_valid'] = False

        # 复杂度评分 (最多30分)
        unique_chars = len(set(password))
        complexity_ratio = unique_chars / length if length > 0 else 0
        result['score'] += int(complexity_ratio * 30)

        # 生成建议
        if not has_uppercase and not config['require_uppercase']:
            result['suggestions'].append('建议添加大写字母以提高安全性')

        if not has_special and not config['require_special']:
            result['suggestions'].append('建议添加特殊字符以提高安全性')

        if length < 12:
            result['suggestions'].append('建议使用12位以上的密码以提高安全性')

        # 生成警告
        if result['score'] < 50:
            result['warnings'].append('密码强度较弱，建议改进')
        elif result['score'] < 70:
            result['warnings'].append('密码强度中等，可以进一步改进')

        return result

    def get_strength_requirements(self, strength: str = '') -> Dict[str, Any]:
        """获取指定强度级别的要求

        Args:
            strength: 强度级别，如果为None则返回当前配置

        Returns:
            Dict: 强度要求配置
        """
        if strength and strength in self._strength_configs:
            config = self._config.copy()
            config.update(self._strength_configs[strength])
            return config
        return self._config.copy()

    def generate_password_tips(self, strength: str = '') -> List[str]:
        """生成密码创建提示

        Args:
            strength: 强度级别

        Returns:
            List[str]: 提示信息列表
        """
        config = self.get_strength_requirements(strength)
        tips = []

        tips.append(f'密码长度应为 {config["min_length"]}-{config["max_length"]} 位')

        requirements = []
        if config['require_lowercase']:
            requirements.append('小写字母')
        if config['require_uppercase']:
            requirements.append('大写字母')
        if config['require_digits']:
            requirements.append('数字')
        if config['require_special']:
            requirements.append('特殊字符')

        if requirements:
            tips.append(f'必须包含: {", ".join(requirements)}')

        tips.append('避免使用连续字符、生日、姓名等容易猜测的信息')
        tips.append('建议使用随机组合或有意义的短语')

        return tips

    async def is_password_valid(self, password: str, strength: str = '') -> bool:
        """简化的异步密码验证方法，只返回是否有效

        Args:
            password: 待验证的密码明文
            strength: 可选的强度级别

        Returns:
            bool: 密码是否有效
        """
        result = await self.validate_password(password, strength)
        return result['is_valid']

    async def get_password_score(self, password: str, strength: str = '') -> int:
        """异步获取密码强度评分

        Args:
            password: 待验证的密码明文
            strength: 可选的强度级别

        Returns:
            int: 密码强度评分 (0-100)
        """
        result = await self.validate_password(password, strength)
        return result['score']

    # 同步版本的方法，用于非异步环境
    def validate_password_sync(self, password: str, strength: str = '') -> Dict[str, Any]:
        """同步版本的密码验证方法

        Args:
            password: 待验证的密码明文
            strength: 可选的强度级别

        Returns:
            Dict: 验证结果
        """
        # 在新的事件循环中运行异步方法
        try:
            # 尝试获取当前事件循环
            try:
                asyncio.get_running_loop()
                # 如果当前线程已有运行中的事件循环，创建新线程执行
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.validate_password(password, strength))
                    return future.result()
            except RuntimeError:
                # 没有运行中的事件循环，直接创建新的
                return asyncio.run(self.validate_password(password, strength))
        except Exception as e:
            # 发生错误时返回基本的错误结果
            return {
                'is_valid': False,
                'score': 0,
                'errors': [f'密码验证失败: {str(e)}'],
                'warnings': [],
                'suggestions': []
            }

    def is_password_valid_sync(self, password: str, strength: str = '') -> bool:
        """同步版本的密码有效性检查

        Args:
            password: 待验证的密码明文
            strength: 可选的强度级别

        Returns:
            bool: 密码是否有效
        """
        result = self.validate_password_sync(password, strength)
        return result['is_valid']

    def get_password_score_sync(self, password: str, strength: str = '') -> int:
        """同步版本的密码强度评分

        Args:
            password: 待验证的密码明文
            strength: 可选的强度级别

        Returns:
            int: 密码强度评分 (0-100)
        """
        result = self.validate_password_sync(password, strength)
        return result['score']



