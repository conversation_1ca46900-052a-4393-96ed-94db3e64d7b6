"""
4inWar 双线程信号通信架构 - 主程序入口
实现主线程(Qt) + 子线程(asyncio) 的信号通信机制
"""

import sys
import os
from PySide6.QtWidgets import QApplication
from tool_kit import Logger
from business_thread_manager import BusinessThreadedManager
from ui_message_hub import UIMessageHub
from ui_window import MainWindow
from ui_game_core import UIGameCore


def main():
    """程序入口点 - 纯Qt主线程"""
    print("=" * 60)
    print("4inWar 双线程信号通信架构")
    print("=" * 60)
    print("架构: 主线程(Qt事件循环) + 子线程(asyncio事件循环)")
    print("=" * 60)

    # 创建Qt应用（纯Qt，不混合asyncio）
    app = QApplication(sys.argv)
    print("Qt应用创建完成")

    # 创建日志实例
    logger = Logger()

    # 创建业务线程管理器（在try外部创建，确保异常处理时可访问）
    business_thread_manager = None

    try:
        # 创建UI消息中心
        ui_message_hub = UIMessageHub(logger)

        # 创建业务线程管理器，传入信号实例
        business_thread_manager = BusinessThreadedManager("business_thread", logger, ui_message_hub.signals.business_signal, ui_message_hub.signals.ui_signal)

        # 创建主窗口实例
        print("创建主窗口...")
        mainwindow = MainWindow(app, logger, ui_message_hub)

        # 创建UI游戏核心实例
        print("创建UI游戏核心...")
        ui_game_core = UIGameCore(mainwindow, logger)

        # 测试时自动登入 - 支持环境变量
        client_name = os.environ.get('CLIENT_NAME', '施恩铭')
        client_password = os.environ.get('CLIENT_PASSWORD', 'q1')

        mainwindow.login_widget.login(client_name, client_password)

        print("主线程开始运行Qt事件循环...")
        result = app.exec()  # 启动Qt事件循环

        return result

    except RuntimeError as e:
        print(f"运行时错误: {e}")
        return 1
    except Exception as e:
        print(f"程序异常: {e}")
        return 1
    finally:
        if business_thread_manager:
            business_thread_manager.stop()

if __name__ == "__main__":
    sys.exit(main())

