import asyncio
import threading


class AdminConsole:
    """管理员控制台 - 负责：
    1. 调用security_validator进行开机检测、认证
    2. 通过message_hub发送admin_command消息
    3. 被message_hub根据admin_response进行调用处理
    4. 在主线程事件循环中运行
    """
    
    def __init__(self, logger, security_validator):
        """初始化管理员控制台"""
        self.logger = logger
        self.is_authenticated = False
        self.security_validator = security_validator
        self.message_hub = None  # MessageHub引用，用于发送命令
        self.logger.info("管理员控制台初始化完成")

    def set_message_hub(self, message_hub):
        """设置MessageHub引用

        Args:
            message_hub: MessageHub实例
        """
        self.message_hub = message_hub
        self.logger.info("MessageHub引用已设置")

    async def phase_one_loop(self, logger):
        """第一阶段：管理员认证循环"""
        while True:
            try:
                # 处理admin_console的认证
                auth_result = await self.process_authentication()
                if auth_result == "success":
                    logger.info("管理员认证成功，准备启动网络模块")
                    return True
                elif auth_result == "exit":
                    logger.info("管理员选择退出")
                    return False
                elif auth_result == "retry":
                    # 继续认证循环
                    continue

            except KeyboardInterrupt:
                logger.info("收到用户中断信号 (Ctrl+C)")
                print("\n收到退出信号，正在停止服务...")
                return False
            except Exception as e:
                logger.error(f"第一阶段循环异常: {e}")
                await asyncio.sleep(1)

    async def phase_two_loop(self):
        """第二阶段：正常服务循环"""
        self.logger.info("=== 第二阶段：正常服务模式 ===")
        print("服务器已启动，按 Ctrl+C 停止...")

        try:
            while True:
                # 处理admin命令
                # await self.process_commands()

                # 其他服务器管理逻辑
                await asyncio.sleep(0.5)

        except KeyboardInterrupt:
            self.logger.info("收到用户中断信号 (Ctrl+C)")
            print("\n收到退出信号，正在停止服务...")
        except Exception as e:
            self.logger.error(f"第二阶段循环异常: {e}")
            raise

    async def process_authentication(self) -> str:
        """处理管理员认证
        Returns:
            str: "success" - 认证成功, "retry" - 重试, "exit" - 按ESC退出
        """
        self.logger.info("=== 管理员认证阶段 ===")
        print("\n=== 4inWar 服务器管理员认证 ===")
        # try:
        #     password = await self.get_terminal_input("请输入管理员密码(按ESC退出): ")

        #     # 检查是否按下了ESC键
        #     if password == "ESC_PRESSED":
        #         self.logger.info("管理员按ESC键退出认证")
        #         confirmation = await self.get_terminal_input('✗ 管理员取消认证，想要停止服务器吗？(Y/N): ')
        #         if confirmation.lower() == "y":
        #             return "exit"
        #         else:
        #             return "retry"

        #     # 检查密码是否为空
        #     if not password.strip():
        #         print("✗ 密码不能为空，请重试...")
        #         return "retry"

        #     # 验证密码
        #     if await self.security_validator.verify_security_key(password):
        #         self.logger.info("管理员密码正确")
        #         self.is_authenticated = True
        #         print("✓ 管理员认证成功！准备启动网络模块...")
        #         return "success"
        #     else:
        #         print("✗ 登录失败，请重试...")
        #         return "retry"
        # except Exception as e:
        #     self.logger.error(f"认证过程异常: {e}")
        #     return "retry"
        return "success"
    
    # async def get_terminal_input(self, prompt: str = "") -> str:
    #     """使用pynput库获取终端输入

    #     功能：
    #     1. 获取用户输入的字符串（回车确认）
    #     2. 监测ESC键执行特定操作（返回特殊标识）
    #     3. 其他错误不会影响这两个核心功能

    #     Returns:
    #         str: 用户输入的字符串，或 "ESC_PRESSED" 表示按下了ESC键
    #     """

    #     # 输入状态控制
    #     input_buffer = []
    #     input_complete = threading.Event()
    #     result = {"value": "", "esc_pressed": False}

    #     def on_key_press(key):
    #         """键盘按键处理"""
    #         try:
    #             if key == keyboard.Key.esc:
    #                 # ESC键被按下
    #                 result["esc_pressed"] = True
    #                 input_complete.set()

    #             elif key == keyboard.Key.enter:
    #                 # 回车键确认输入
    #                 result["value"] = ''.join(input_buffer)
    #                 input_complete.set()

    #             elif key == keyboard.Key.backspace:
    #                 # 退格键
    #                 if input_buffer:
    #                     input_buffer.pop()
    #                     # 在终端显示退格效果
    #                     print('\b \b', end='', flush=True)

    #             elif hasattr(key, 'char') and key.char is not None:
    #                 # 普通字符输入
    #                 if key.char.isprintable():
    #                     input_buffer.append(key.char)
    #                     # 在终端显示字符（密码输入时可以显示*）
    #                     if "密码" in prompt.lower() or "password" in prompt.lower():
    #                         print('*', end='', flush=True)
    #                     else:
    #                         print(key.char, end='', flush=True)
    #         except Exception:
    #             # 忽略键盘处理中的任何异常，不影响核心功能
    #             pass

    #     def input_worker():
    #         """在单独线程中处理键盘监听"""
    #         try:
    #             # 显示提示信息
    #             if prompt:
    #                 print(prompt, end='', flush=True)
    #             else:
    #                 print('', end='', flush=True)

    #             # 开始键盘监听
    #             listener = keyboard.Listener(on_press=on_key_press)
    #             listener.start()

    #             # 等待输入完成
    #             input_complete.wait()
    #             listener.stop()

    #         except Exception:
    #             # 如果pynput出现问题，回退到传统input方式
    #             try:
    #                 fallback_input = input(prompt if prompt else "请输入: ")
    #                 result["value"] = fallback_input
    #             except:
    #                 result["value"] = ""
    #             finally:
    #                 input_complete.set()

    #     # 在后台线程中运行输入处理
    #     import concurrent.futures

    #     with concurrent.futures.ThreadPoolExecutor() as executor:
    #         # 提交输入任务到线程池
    #         future = executor.submit(input_worker)

    #         # 等待输入完成
    #         while not input_complete.is_set():
    #             await asyncio.sleep(0.1)

    #         # 确保线程完成
    #         try:
    #             future.result(timeout=1.0)
    #         except:
    #             pass

    #     print()  # 换行

    #     # 返回结果
    #     if result["esc_pressed"]:
    #         return "ESC_PRESSED"
    #     else:
    #         return result["value"]

    # async def process_commands(self):
    #     """处理管理员命令（占位方法，将来扩展）"""
    #     await self.get_terminal_input("请输入管理员命令: ")
    #     # 这里将来可以添加管理员命令处理逻辑
    #     # 例如：监听特定按键、处理管理员指令等
    #     pass
