import sys
from PySide6.QtWidgets import (QApplication, QGraphicsView, QGraphicsScene,
                              QVBoxLayout, QWidget, QPushButton, QHBoxLayout)
from PySide6.QtSvgWidgets import QGraphicsSvgItem
from PySide6.QtCore import Qt
from PySide6.QtGui import QPainter

class SvgViewer(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        # 创建布局
        layout = QVBoxLayout()
        
        # 创建图形视图和场景
        self.scene = QGraphicsScene()
        self.view = QGraphicsView(self.scene)
        self.view.setRenderHint(QPainter.RenderHint.Antialiasing)
        layout.addWidget(self.view)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 创建切换SVG的按钮
        btn1 = QPushButton("显示SVG 1")
        btn1.clicked.connect(lambda: self.change_svg("blank.svg"))
        button_layout.addWidget(btn1)
        
        btn2 = QPushButton("显示SVG 2")
        btn2.clicked.connect(lambda: self.change_svg("00.svg"))
        button_layout.addWidget(btn2)
        
        btn3 = QPushButton("显示SVG 3")
        btn3.clicked.connect(lambda: self.change_svg("01.svg"))
        button_layout.addWidget(btn3)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        self.setWindowTitle('QGraphicsSvgItem动态切换示例')
        self.resize(600, 500)
        
        # 初始SVG项目
        self.svg_item = QGraphicsSvgItem()
        self.scene.addItem(self.svg_item)
        
        # 加载初始SVG
        self.change_svg("blank.svg")
    
    def change_svg(self, svg_path):
        # 方案A：直接使用文件路径创建新的QGraphicsSvgItem
        try:
            # 移除旧的SVG项目
            if hasattr(self, 'svg_item') and self.svg_item:
                self.scene.removeItem(self.svg_item)

            # 创建新的SVG项目
            self.svg_item = QGraphicsSvgItem(svg_path)

            # 检查是否成功加载
            if self.svg_item.renderer() and self.svg_item.renderer().isValid():
                self.scene.addItem(self.svg_item)

                # 调整视图大小以适应SVG
                size = self.svg_item.renderer().defaultSize()
                self.svg_item.setPos(0, 0)
                self.scene.setSceneRect(0, 0, size.width(), size.height())
                self.view.fitInView(self.scene.sceneRect(), Qt.AspectRatioMode.KeepAspectRatio)
                print(f"成功加载SVG文件: {svg_path}")
            else:
                print(f"无法加载SVG文件: {svg_path}")
                # 创建一个空的SVG项目作为占位符
                self.svg_item = QGraphicsSvgItem()
                self.scene.addItem(self.svg_item)
        except Exception as e:
            print(f"加载SVG文件时发生异常: {e}")
            # 创建一个空的SVG项目作为占位符
            self.svg_item = QGraphicsSvgItem()
            self.scene.addItem(self.svg_item)
    
    def resizeEvent(self, event):
        # 当窗口大小改变时，调整视图以适应SVG
        self.view.fitInView(self.scene.sceneRect(), Qt.AspectRatioMode.KeepAspectRatio)
        super().resizeEvent(event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    viewer = SvgViewer()
    viewer.show()
    sys.exit(app.exec_())