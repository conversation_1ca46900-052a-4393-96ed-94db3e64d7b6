import sys
from PySide6.QtWidgets import (QApplication, QGraphicsView, QGraphicsScene,
                              QVBoxLayout, QWidget, QPushButton, QHBoxLayout)
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtSvgWidgets import QGraphicsSvgItem
from PySide6.QtCore import Qt
from PySide6.QtGui import QPainter

class SvgViewer(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        # 创建布局
        layout = QVBoxLayout()
        
        # 创建图形视图和场景
        self.scene = QGraphicsScene()
        self.view = QGraphicsView(self.scene)
        self.view.setRenderHint(QPainter.RenderHint.Antialiasing)
        layout.addWidget(self.view)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 创建切换SVG的按钮
        btn1 = QPushButton("显示SVG 1")
        btn1.clicked.connect(lambda: self.change_svg("image1.svg"))
        button_layout.addWidget(btn1)
        
        btn2 = QPushButton("显示SVG 2")
        btn2.clicked.connect(lambda: self.change_svg("image2.svg"))
        button_layout.addWidget(btn2)
        
        btn3 = QPushButton("显示SVG 3")
        btn3.clicked.connect(lambda: self.change_svg("image3.svg"))
        button_layout.addWidget(btn3)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        self.setWindowTitle('QGraphicsSvgItem动态切换示例')
        self.resize(600, 500)
        
        # 初始SVG项目
        self.svg_item = QGraphicsSvgItem()
        self.scene.addItem(self.svg_item)
        
        # 加载初始SVG
        self.change_svg("initial.svg")
    
    def change_svg(self, svg_path):
        renderer = QSvgRenderer()
        if renderer.load(svg_path):
            self.svg_item.setSharedRenderer(renderer)
            # 调整视图大小以适应SVG
            size = renderer.defaultSize()
            self.svg_item.setPos(0, 0)
            self.scene.setSceneRect(0, 0, size.width(), size.height())
            self.view.fitInView(self.scene.sceneRect(), Qt.AspectRatioMode.KeepAspectRatio)
        else:
            print(f"无法加载SVG文件: {svg_path}")
    
    def resizeEvent(self, event):
        # 当窗口大小改变时，调整视图以适应SVG
        self.view.fitInView(self.scene.sceneRect(), Qt.AspectRatioMode.KeepAspectRatio)
        super().resizeEvent(event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    viewer = SvgViewer()
    viewer.show()
    sys.exit(app.exec_())