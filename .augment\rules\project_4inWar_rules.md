---
type: "always_apply"
---

# 4inWar项目 - AI助手执行规范 (User Guidelines)

## 🚨 强制执行规则 (MANDATORY EXECUTION RULES)

### ⚡ 命令执行前必须检查清单 (PRE-EXECUTION CHECKLIST)
**在执行任何Python命令或PowerShell命令前，AI必须：**

1. ✅ **确认Python环境**：使用完整路径 `D:\SoftWare\anaconda3\envs\My_ENV\python.exe`
2. ✅ **确认工作目录**：`d:\Y2K\Cloud\MyCloud\Code\Python\Projects\4inWar`
3. ✅ **确认PowerShell语法**：使用分号(`;`)连接命令，**禁止使用** `&&`
4. ✅ **声明环境配置**：在命令执行前明确声明遵循环境配置

### 🔧 环境配置验证模板 (ENVIRONMENT VERIFICATION TEMPLATE)
**每次执行命令前必须显示此检查：**
```
🔧 环境配置检查：
- Python路径：D:\SoftWare\anaconda3\envs\My_ENV\python.exe ✅
- 工作目录：d:\Y2K\Cloud\MyCloud\Code\Python\Projects\4inWar ✅  
- PowerShell语法：使用分号(;)连接命令 ✅
- 项目环境：4inWar/My_ENV ✅
```

## 📋 环境配置详情

### 🐍 Python环境
- **虚拟环境名称**：My_ENV
- **完整路径**：`D:\SoftWare\anaconda3\envs\My_ENV`
- **Python执行器**：`D:\SoftWare\anaconda3\envs\My_ENV\python.exe`
- **包管理器**：使用Anaconda管理虚拟环境

### 💻 操作系统环境
- **操作系统**：Windows
- **终端**：PowerShell
- **工作目录**：`d:\Y2K\Cloud\MyCloud\Code\Python\Projects\4inWar`

### ⚙️ PowerShell语法规则
- **命令连接**：使用分号 `;` 
  - ✅ 正确：`command1; command2; command3`
  - ❌ 错误：`command1 && command2 && command3`
- **路径分隔符**：使用反斜杠 `\` (Windows标准)


## 🚫 违规处理 (VIOLATION HANDLING)

### 如果AI未遵循上述规则：
1. **用户应立即指出违规**
2. **AI必须：**
   - 承认违规并道歉
   - 重新执行正确的命令
   - 确认已重新读取User Guidelines
   - 使用正确的环境配置重新操作

### 常见违规示例：
- ❌ 使用 `python` 而不是完整路径
- ❌ 使用 `&&` 连接PowerShell命令
- ❌ 未声明环境配置检查
- ❌ 在错误的工作目录下执行命令

## 📝 标准命令模板

### Python脚本执行：
```powershell
# 环境检查
🔧 环境配置检查：[显示上述模板]

# 执行命令
D:\SoftWare\anaconda3\envs\My_ENV\python.exe [脚本路径]
```

### 多命令执行：
```powershell
# 环境检查
🔧 环境配置检查：[显示上述模板]

# 执行命令
cd d:\Y2K\Cloud\MyCloud\Code\Python\Projects\4inWar; D:\SoftWare\anaconda3\envs\My_ENV\python.exe [脚本]
```

## 🔄 持续改进机制

### 记忆机制：
- 使用remember工具保存关键环境配置
- 每次会话开始时重新确认环境设置
- 建立检查点确保跨对话一致性

### 验证机制：
- 每次命令执行前强制显示环境检查
- 用户可随时要求重新验证环境配置

## 🧪 测试和验证规范

### 测试文件执行标准：
```powershell
# 环境检查（必须显示）
🔧 环境配置检查：
- Python路径：D:\SoftWare\anaconda3\envs\My_ENV\python.exe ✅
- 工作目录：d:\Y2K\Cloud\MyCloud\Code\Python\Projects\4inWar ✅
- PowerShell语法：使用分号(;)连接命令 ✅
- 项目环境：4inWar/My_ENV ✅

### 包管理规范：
- **依赖安装**：使用Anaconda包管理器而非手动编辑配置文件
- **环境激活**：通过完整路径调用，无需手动激活环境
- **包安装命令**：`D:\SoftWare\anaconda3\envs\My_ENV\Scripts\conda.exe install [package]`

## 📊 开发和调试规范

### 代码编辑规范：
- **保守修改**：尊重代码库，进行保守的修改

### 日志和监控：
- **服务器日志**：存储在server/logs目录
- **客户端日志**：存储在client/logs目录
- **监控日志**：使用专用文件分离监控日志
- **⚠️ 日志延迟写入机制**：日志系统采用缓存延后批量写入，检查日志前必须等待足够时间（建议等待10-15秒）让日志完全写入文件，否则只能看到旧数据

---

**⚠️ 重要提醒：此文档为强制执行规范，AI助手必须严格遵循，不得例外。**