#!/usr/bin/env python3
"""
环境检测工具
自动检测不同电脑下的Python路径和项目路径
"""

import os
import sys
import platform
from typing import Optional, List, Dict


class EnvironmentDetector:
    """环境检测器 - 自动适配不同电脑的环境配置"""
    
    def __init__(self):
        self.system_info = {
            'platform': platform.system(),
            'machine': platform.machine(),
            'python_version': platform.python_version(),
            'current_user': os.getenv('USERNAME') or os.getenv('USER', 'unknown')
        }
    
    def detect_python_path(self, env_name: str = "My_ENV") -> str:
        """检测Python环境路径
        
        Args:
            env_name: 虚拟环境名称，默认为 "My_ENV"
            
        Returns:
            str: Python解释器的完整路径
        """
        print(f"🔧 环境配置检查：")
        print(f"- 操作系统：{self.system_info['platform']} ✅")
        print(f"- 当前用户：{self.system_info['current_user']} ✅")
        print(f"- Python版本：{self.system_info['python_version']} ✅")
        
        # 首先检查当前Python解释器是否在目标环境中
        current_python = sys.executable
        if env_name in current_python and os.path.exists(current_python):
            print(f"- Python路径：{current_python} ✅ (当前解释器)")
            return current_python
        
        # 定义可能的Python路径模式
        possible_patterns = self._get_python_path_patterns(env_name)
        
        # 遍历可能的路径
        for pattern in possible_patterns:
            if os.path.exists(pattern):
                print(f"- Python路径：{pattern} ✅ (自动检测)")
                return pattern
        
        # 如果都找不到，使用当前Python解释器作为后备
        print(f"- Python路径：{current_python} ⚠️ (后备方案)")
        print(f"  警告：未找到 {env_name} 环境，使用当前Python解释器")
        return current_python
    
    def detect_project_path(self, project_name: str = "4inWar") -> str:
        """检测项目路径
        
        Args:
            project_name: 项目名称，默认为 "4inWar"
            
        Returns:
            str: 项目的完整路径
        """
        # 首先检查当前工作目录
        current_dir = os.getcwd()
        if self._is_valid_project_path(current_dir, project_name):
            print(f"- 工作目录：{current_dir} ✅ (当前目录)")
            return current_dir
        
        # 定义可能的项目路径模式
        possible_patterns = self._get_project_path_patterns(project_name)
        
        # 遍历可能的路径
        for pattern in possible_patterns:
            if self._is_valid_project_path(pattern, project_name):
                print(f"- 工作目录：{pattern} ✅ (自动检测)")
                return pattern
        
        # 如果都找不到，使用当前目录作为后备
        print(f"- 工作目录：{current_dir} ⚠️ (后备方案)")
        print(f"  警告：未找到标准 {project_name} 项目路径，使用当前目录")
        return current_dir
    
    def _get_python_path_patterns(self, env_name: str) -> List[str]:
        """获取可能的Python路径模式"""
        user = self.system_info['current_user']
        
        patterns = [
            # Windows Anaconda 常见路径
            rf"D:\SoftWare\anaconda3\envs\{env_name}\python.exe",
            rf"C:\Users\<USER>\anaconda3\envs\{env_name}\python.exe",
            rf"C:\Users\<USER>\Anaconda3\envs\{env_name}\python.exe",
            rf"C:\Anaconda3\envs\{env_name}\python.exe",
            rf"D:\Anaconda3\envs\{env_name}\python.exe",
            
            # Miniconda 路径
            rf"C:\Users\<USER>\miniconda3\envs\{env_name}\python.exe",
            rf"C:\miniconda3\envs\{env_name}\python.exe",
            
            # 其他可能的路径
            rf"C:\Python\envs\{env_name}\python.exe",
            rf"D:\Python\envs\{env_name}\python.exe",
        ]
        
        # Linux/Mac 路径（如果需要）
        if self.system_info['platform'] != 'Windows':
            patterns.extend([
                f"/home/<USER>/anaconda3/envs/{env_name}/bin/python",
                f"/opt/anaconda3/envs/{env_name}/bin/python",
                f"/usr/local/anaconda3/envs/{env_name}/bin/python",
            ])
        
        return patterns
    
    def _get_project_path_patterns(self, project_name: str) -> List[str]:
        """获取可能的项目路径模式"""
        patterns = [
            # 常见的项目路径
            rf"d:\Y2K\Cloud\MyCloud\Code\Python\Projects\{project_name}",
            rf"d:\Cloud\MyCloud\Code\Python\Projects\{project_name}",
            rf"c:\Projects\{project_name}",
            rf"d:\Projects\{project_name}",
            rf"c:\Code\{project_name}",
            rf"d:\Code\{project_name}",
            
            # 用户目录下的项目
            rf"C:\Users\<USER>\Documents\{project_name}",
            rf"C:\Users\<USER>\Desktop\{project_name}",
            rf"C:\Users\<USER>\Projects\{project_name}",
        ]
        
        # Linux/Mac 路径（如果需要）
        if self.system_info['platform'] != 'Windows':
            user = self.system_info['current_user']
            patterns.extend([
                f"/home/<USER>/Projects/{project_name}",
                f"/home/<USER>/Documents/{project_name}",
                f"/home/<USER>/Desktop/{project_name}",
                f"/opt/{project_name}",
                f"/usr/local/{project_name}",
            ])
        
        return patterns
    
    def _is_valid_project_path(self, path: str, project_name: str) -> bool:
        """检查是否是有效的项目路径"""
        if not os.path.exists(path):
            return False
        
        # 检查关键文件是否存在
        key_files = [
            "server/main.py",
            "client/main.py",
            "server/data/database.py"
        ]
        
        for key_file in key_files:
            if not os.path.exists(os.path.join(path, key_file)):
                return False
        
        return True
    
    def get_environment_config(self) -> Dict[str, str]:
        """获取完整的环境配置"""
        config = {
            'python_path': self.detect_python_path(),
            'project_path': self.detect_project_path(),
            'platform': self.system_info['platform'],
            'user': self.system_info['current_user']
        }
        
        print("- PowerShell语法：使用分号(;)连接命令 ✅")
        print("- 项目环境：4inWar/My_ENV ✅")
        print()
        
        return config
    
    def validate_environment(self) -> bool:
        """验证环境配置是否正确"""
        config = self.get_environment_config()
        
        # 检查Python路径
        if not os.path.exists(config['python_path']):
            print(f"❌ Python路径无效: {config['python_path']}")
            return False
        
        # 检查项目路径
        if not self._is_valid_project_path(config['project_path'], "4inWar"):
            print(f"❌ 项目路径无效: {config['project_path']}")
            return False
        
        print("✅ 环境配置验证通过")
        return True


# 全局实例，可以直接导入使用
env_detector = EnvironmentDetector()


def get_python_path() -> str:
    """快捷函数：获取Python路径"""
    return env_detector.detect_python_path()


def get_project_path() -> str:
    """快捷函数：获取项目路径"""
    return env_detector.detect_project_path()


def get_environment_config() -> Dict[str, str]:
    """快捷函数：获取环境配置"""
    return env_detector.get_environment_config()


if __name__ == "__main__":
    # 测试环境检测
    print("=" * 60)
    print("🧪 环境检测测试")
    print("=" * 60)
    
    detector = EnvironmentDetector()
    config = detector.get_environment_config()
    
    print("📋 检测结果:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    print("\n🔍 验证环境...")
    is_valid = detector.validate_environment()
    
    print(f"\n🎯 环境状态: {'✅ 正常' if is_valid else '❌ 异常'}")
