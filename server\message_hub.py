import asyncio
from typing import Dict, Any, Optional


class MessageHub:
    """总线消息路由 - 消息总线的核心路由节点"""
    def __init__(self, logger, admin_console, network_receive_queue, pending_queue, response_queue, network_send_queue):
        """初始化总线消息路由
        Args:
            logger: 日志记录器
            admin_console: 管理员控制台
            network_receive_queue: 网络接收消息队列
            pending_queue: 待处理消息队列
            response_queue: 响应消息队列
            network_send_queue: 网络发送队列
        """
        self.logger = logger
        self.admin_console = admin_console
        self.network_receive_queue = network_receive_queue
        self.pending_queue = pending_queue
        self.response_queue = response_queue
        self.network_send_queue = network_send_queue
        self.is_running = False

        self.logger.info("总线消息路由初始化完成")


    async def start(self):
        """启动总线消息路由"""
        if self.is_running:
            self.logger.warning("总线消息路由已在运行")
            return

        self.is_running = True
        self.logger.info("总线消息路由启动")

        try:
            # 启动双向消息处理循环
            await asyncio.gather(
                self._network_receive_loop(),
                self._response_processing_loop()
            )
        except Exception as e:
            self.logger.error(f"总线消息路由运行异常: {e}")
            raise
        finally:
            self.is_running = False
            self.logger.info("总线消息路由已停止")


    async def stop(self):
        """停止总线消息路由"""
        self.logger.info("正在停止总线消息路由...")
        self.is_running = False


    async def _network_receive_loop(self):
        """网络接收消息处理循环

        从network_receive_queue接收消息并路由到pending_queue
        """
        self.logger.info("开始网络消息路由处理循环")

        while self.is_running:
            try:
                # 使用 asyncio.wait 优雅地处理队列获取和状态检查
                get_task = asyncio.create_task(self.network_receive_queue.get())
                sleep_task = asyncio.create_task(asyncio.sleep(0.02))  # 优化：从100ms降低到20ms

                try:
                    # 等待消息获取或超时，哪个先完成就处理哪个
                    done, _ = await asyncio.wait(
                        [get_task, sleep_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )

                    if get_task in done:
                        # 有消息到达，获取并处理
                        receive_message = await get_task
                        sleep_task.cancel()  # 取消睡眠任务

                        message_type = receive_message.get('type', 'unknown')
                        socket_id = receive_message.get('socket_id', '')
                        self.logger.timing("HUB_RECV", socket_id, message_type)  # 性能日志
                        self.logger.info(f"📥 [MessageHub] 收到消息: {message_type}")

                        await self._route_to_pending_queue(receive_message)
                    else:
                        # 超时，取消获取任务，继续循环检查is_running状态
                        get_task.cancel()
                        # sleep_task 已完成，无需取消

                except asyncio.CancelledError:
                    self.logger.info(f"🛑 [MessageHub] 接收到取消信号，清理任务")
                    # 清理未完成的任务
                    if not get_task.done():
                        get_task.cancel()
                    if not sleep_task.done():
                        sleep_task.cancel()
                    break

            except Exception as e:
                self.logger.error(f"❌ [MessageHub] 网络接收消息处理循环异常: {e}")
                await asyncio.sleep(0.1)  # 异常后短暂等待
                

    async def _route_to_pending_queue(self, message: Dict[str, Any]):
        """处理网络接收消息
        从network_receive_queue接收的消息，路由到pending_queue
        Args:
            message: 接收消息字典，格式: {'type': str, 'data': dict, 'socket_id': str, ...}
        """
        try:
            # 网络层已完成格式验证，直接处理
            message_type = message.get('type', '')
            socket_id = message.get('socket_id', 'unknown')
            self.logger.info(f"✅ [MessageHub] 处理消息: {message_type} (ID: {socket_id})")

            # 路由消息到pending_queue
            await self.pending_queue.put(message)

        except Exception as e:
            self.logger.error(f"❌ [MessageHub] 接收消息处理异常: {e}")
            import traceback
            self.logger.error(f"❌ [MessageHub] 异常堆栈: {traceback.format_exc()}")


    async def _response_processing_loop(self):
        """响应消息处理循环
        从response_queue接收消息并路由到network_send_queue
        """
        self.logger.info("开始响应消息处理循环")

        while self.is_running:
            try:
                # 使用 asyncio.wait 优雅地处理队列获取和状态检查
                get_task = asyncio.create_task(self.response_queue.get())
                sleep_task = asyncio.create_task(asyncio.sleep(0.02))  # 优化：从100ms降低到20ms

                try:
                    # 等待消息获取或超时，哪个先完成就处理哪个
                    done, _ = await asyncio.wait(
                        [get_task, sleep_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )

                    if get_task in done:
                        # 有消息到达，获取并处理
                        response_message = await get_task
                        sleep_task.cancel()  # 取消睡眠任务

                        # 性能日志
                        message_type = response_message.get('type', 'unknown')
                        socket_id = response_message.get('socket_id', '')
                        self.logger.timing("HUB_RESP", socket_id, message_type)

                        await self._process_response_message(response_message)
                    else:
                        # 超时，取消获取任务，继续循环检查is_running状态
                        get_task.cancel()
                        # sleep_task 已完成，无需取消

                except asyncio.CancelledError:
                    # 清理未完成的任务
                    if not get_task.done():
                        get_task.cancel()
                    if not sleep_task.done():
                        sleep_task.cancel()
                    break

            except Exception as e:
                self.logger.error(f"响应消息处理循环异常: {e}")
                await asyncio.sleep(0.1)  # 异常后短暂等待


    async def _process_response_message(self, message: Dict[str, Any]):
        """处理并路由响应消息

        Args:
            message: 响应消息字典，格式: {'type': str, 'data': dict, 'socket_id': str, ...}
        """
        try:
            socket_id = message.get('socket_id', 'unknown')
            message_type = message.get('type', '')
            self.logger.info(f"📤 [MessageHub] 发送响应: {message_type} (ID: {socket_id})")

            # 路由消息到正确的目标
            if message_type == 'admin_response':
                # Admin消息 - 路由到admin_console
                if self.admin_console:
                    await self.admin_console.receive_response(message)
                    self.logger.debug(f"已路由admin响应到控制台: {message_type}")
                else:
                    self.logger.warning("收到admin响应但admin_console未设置")
            else:
                # 网络消息 - 通过队列发送
                if not self.network_send_queue:
                    self.logger.warning("❌ [MessageHub] 收到网络消息但network_send_queue未设置")
                    return
                await self.network_send_queue.put(message)
                self.logger.info(f"📊 [MessageHub] 消息已放入network_send_queue")

        except Exception as e:
            # 这里只捕获总线消息路由层面的异常，网络异常由network处理
            self.logger.error(f"❌ [MessageHub] 响应消息处理异常: {e}")
            import traceback
            self.logger.error(f"❌ [MessageHub] 响应异常堆栈: {traceback.format_exc()}")


# 工厂函数，用于在主线程中创建和启动MessageHub
async def create_and_run_message_hub(logger, network_receive_queue, pending_queue, response_queue, admin_console, network_send_queue):
    """在主线程中创建并运行总线消息路由

    Args:
        logger: 日志记录器
        network_receive_queue: 网络接收消息队列
        pending_queue: 待处理消息队列
        response_queue: 响应消息队列
        admin_console: 管理员控制台
        network_send_queue: 网络发送队列
    """
    message_hub = MessageHub(logger, network_receive_queue, pending_queue, response_queue, admin_console, network_send_queue)
    await message_hub.start()
