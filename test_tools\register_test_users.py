#!/usr/bin/env python3
"""
注册测试用户脚本
为4inWar项目注册测试用户：陈琪、杨燕超、王锋玮
"""

import sys
import os
import asyncio
import bcrypt
import hashlib

# 添加项目路径到sys.path
project_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_path)
sys.path.insert(0, os.path.join(project_path, 'server'))
sys.path.insert(0, os.path.join(project_path, 'server', 'data'))

from server.data.database import DatabaseManager
from server.tool_kit import Logger

class TestUserRegistrar:
    def __init__(self):
        self.logger = Logger()
        self.db_manager = None
        
        # 测试用户数据
        self.test_users = [
            {'username': '施恩铭', 'password': 'q1'},
            {'username': '陈琪', 'password': 'q1'},
            {'username': '杨燕超', 'password': 'q1'},
            {'username': '王锋玮', 'password': 'q1'},
        ]
    
    async def initialize(self):
        """初始化数据库管理器"""
        try:
            # 数据库文件路径
            db_path = os.path.join(project_path, 'server', 'data', 'game.db')
            self.db_manager = DatabaseManager(db_path, self.logger)
            await self.db_manager.initialize()
            self.logger.info("数据库管理器初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库管理器初始化失败: {e}")
            return False
    
    def hash_password(self, password: str) -> str:
        """对密码进行SHA256哈希，然后用bcrypt加盐"""
        # 先进行SHA256哈希（模拟客户端行为）
        sha256_hash = hashlib.sha256(password.encode('utf-8')).hexdigest()
        # 再用bcrypt加盐（服务器端处理）
        bcrypt_hash = bcrypt.hashpw(sha256_hash.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        return bcrypt_hash
    
    async def register_user(self, username: str, password: str) -> bool:
        """注册单个用户"""
        try:
            # 检查用户是否已存在
            if self.db_manager is None:
                self.logger.error("❌ 数据库管理器未初始化")
                return False
            existing_user = await self.db_manager.select_one(
                table_name="users",
                columns=["username"],
                where_conditions={"username": username}
            )
            
            if existing_user['success'] and existing_user['data']:
                self.logger.info(f"用户 {username} 已存在，跳过注册")
                return True
            
            # 对密码进行哈希处理
            password_hash = self.hash_password(password)
            
            # 插入用户数据
            user_data = {
                'username': username,
                'password_hash': password_hash,
                'total_score': 0,
                'games_played': 0,
                'games_won': 0,
                'is_active': 1,
                'user_level': 1
            }
            
            result = await self.db_manager.insert_or_ignore(
                table_name="users",
                data=user_data,
                returning_fields=['id', 'username', 'created_at']
            )
            
            if result['success']:
                if result['inserted']:
                    self.logger.info(f"✅ 用户 {username} 注册成功")
                    if result['returned_data']:
                        self.logger.info(f"   用户ID: {result['returned_data'].get('id')}")
                        self.logger.info(f"   创建时间: {result['returned_data'].get('created_at')}")
                else:
                    self.logger.info(f"ℹ️ 用户 {username} 已存在")
                return True
            else:
                self.logger.error(f"❌ 用户 {username} 注册失败: {result.get('error')}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 注册用户 {username} 时发生异常: {e}")
            return False
    
    async def register_all_users(self):
        """注册所有测试用户"""
        self.logger.info("开始注册测试用户...")
        
        success_count = 0
        for user_info in self.test_users:
            username = user_info['username']
            password = user_info['password']
            
            if await self.register_user(username, password):
                success_count += 1
        
        self.logger.info(f"注册完成！成功: {success_count}/{len(self.test_users)}")
        return success_count == len(self.test_users)
    
    async def verify_users(self):
        """验证用户是否注册成功"""
        self.logger.info("验证用户注册状态...")
        
        for user_info in self.test_users:
            username = user_info['username']
            if self.db_manager is None:
                self.logger.error("❌ 数据库管理器未初始化")
                return
            result = await self.db_manager.select_one(
                table_name="users",
                columns=["id", "username", "created_at", "is_active"],
                where_conditions={"username": username}
            )
            
            if result['success'] and result['data']:
                user_data = result['data']
                self.logger.info(f"✅ {username}: ID={user_data['id']}, 创建时间={user_data['created_at']}")
            else:
                self.logger.error(f"❌ {username}: 未找到用户记录")
    
    async def cleanup(self):
        """清理资源"""
        if self.db_manager:
            # DatabaseManager 不需要显式关闭
            self.logger.info("资源清理完成")

async def main():
    """主函数"""
    print("=" * 60)
    print("4inWar 测试用户注册工具")
    print("=" * 60)
    
    registrar = TestUserRegistrar()
    
    try:
        # 初始化
        if not await registrar.initialize():
            print("❌ 初始化失败")
            return 1
        
        # 注册用户
        if await registrar.register_all_users():
            print("\n✅ 所有用户注册成功！")
        else:
            print("\n⚠️ 部分用户注册失败")
        
        # 验证用户
        print("\n" + "=" * 40)
        await registrar.verify_users()
        
        print("\n" + "=" * 60)
        print("注册完成！现在可以使用以下用户进行测试：")
        print("1. 施恩铭 (密码: q1)")
        print("2. 陈琪 (密码: q1)")
        print("3. 杨燕超 (密码: q1)")
        print("4. 王锋玮 (密码: q1)")
        print("=" * 60)
        
        return 0
        
    except Exception as e:
        print(f"❌ 程序执行异常: {e}")
        return 1
    finally:
        await registrar.cleanup()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
