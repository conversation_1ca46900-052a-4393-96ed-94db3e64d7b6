"""
客户端工具包模块
包含各种通用的工具类和函数
"""

import threading
from typing import Optional
import logging
import logging.handlers
import os
from datetime import datetime
import inspect


class LogConfig:
    """日志配置类"""
    # 性能模式：只记录性能相关日志到文件
    PERFORMANCE_MODE = False

    # 详细模式：记录所有日志
    VERBOSE_MODE = True

    @classmethod
    def set_performance_mode(cls, enabled: bool):
        """设置性能模式"""
        cls.PERFORMANCE_MODE = enabled
        cls.VERBOSE_MODE = not enabled

    @classmethod
    def set_verbose_mode(cls, enabled: bool):
        """设置详细模式"""
        cls.VERBOSE_MODE = enabled
        if enabled:
            cls.PERFORMANCE_MODE = False


class PerformanceLogger:
    """性能分析日志记录器

    专门用于记录通信时间和性能相关的日志，便于压力测试分析。
    只记录关键的时间节点，减少日志噪音。
    """
    _instance = None
    _lock = threading.Lock()
    log_dir: str = 'client/logs'

    def __new__(cls, log_dir: str = ''):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                if log_dir:
                    cls._instance.log_dir = log_dir
                cls._instance.setup_performance_logger()
            return cls._instance

    def setup_performance_logger(self):
        """设置性能日志记录器"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        current_time = datetime.now().strftime('%Y%m%d')
        perf_log_file = os.path.join(self.log_dir, f'performance_{current_time}.log')

        self.perf_logger = logging.getLogger('PerformanceLogger')
        self.perf_logger.setLevel(logging.INFO)

        # 创建性能日志文件处理器
        self.perf_file_handler = logging.handlers.TimedRotatingFileHandler(
            perf_log_file,
            when='midnight',
            interval=1,
            backupCount=30,  # 保留30天的性能日志
            encoding='utf-8'
        )
        self.perf_file_handler.setLevel(logging.INFO)

        # 设置简化的性能日志格式（便于分析）
        perf_formatter = logging.Formatter(
            '%(asctime)s.%(msecs)03d|%(threadName)s|%(message)s',
            datefmt='%H:%M:%S'
        )
        self.perf_file_handler.setFormatter(perf_formatter)

        # 确保处理器不会重复添加
        self.perf_logger.handlers = []
        self.perf_logger.addHandler(self.perf_file_handler)
        self.perf_logger.propagate = False  # 不传播到根日志器

    def log_timing(self, event: str, socket_id: str = "", message_type: str = ""):
        """记录时间相关事件

        Args:
            event: 事件名称 (如: NET_RECV, HUB_RECV, DC_PROC, HUB_RESP, NET_SEND)
            socket_id: 连接ID
            message_type: 消息类型
        """
        msg = f"{event}|{socket_id}|{message_type}"
        self.perf_logger.info(msg)

    def cleanup_performance(self):
        """清理性能日志系统"""
        try:
            handlers = self.perf_logger.handlers[:]
            for handler in handlers:
                handler.close()
                self.perf_logger.removeHandler(handler)
            PerformanceLogger._instance = None
        except Exception as e:
            print(f"清理性能日志系统时发生错误: {e}")


class Logger:
    """日志记录器工具类

    单例模式的日志记录器，提供统一的日志记录接口。
    支持文件和控制台双重输出，自动按日期轮转日志文件。
    """
    _instance = None
    _lock = threading.Lock()
    log_dir: str = 'client/logs'

    # 声明实例属性
    logger: Optional[logging.Logger]
    file_handler: Optional[logging.handlers.TimedRotatingFileHandler]
    console_handler: Optional[logging.StreamHandler]
    perf_logger: Optional['PerformanceLogger']

    def __new__(cls, log_dir: str = '', logger_name: str = ''):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                # 初始化实例属性
                cls._instance.logger = None
                cls._instance.file_handler = None
                cls._instance.console_handler = None
                cls._instance.perf_logger = None
                # 设置日志目录
                if log_dir:
                    cls._instance.log_dir = log_dir
                # 设置日志器
                cls._instance.setup_logger(logger_name)
                # 初始化性能日志器
                cls._instance.perf_logger = PerformanceLogger(log_dir)
            return cls._instance

    def setup_logger(self, logger_name: str = ''):
        """设置日志记录器"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        current_time = datetime.now().strftime('%Y%m%d')
        # 根据日志目录自动确定日志文件前缀
        if 'client' in self.log_dir:
            log_prefix = 'client'
            default_logger_name = 'ClientLogger'
        else:
            log_prefix = 'server'
            default_logger_name = 'ServerLogger'
        
        log_file = os.path.join(self.log_dir, f'{log_prefix}_{current_time}.log')

        self.logger = logging.getLogger(logger_name or default_logger_name)
        self.logger.setLevel(logging.DEBUG)

        # 创建TimedRotatingFileHandler
        self.file_handler = logging.handlers.TimedRotatingFileHandler(
            log_file,
            when='midnight',
            interval=1,
            backupCount=15,  # 保留15天的日志
            encoding='utf-8'
        )
        # 禁用缓存，立即写入（调试用）
        def force_flush():
            try:
                if self.file_handler and hasattr(self.file_handler, 'stream'):
                    stream = getattr(self.file_handler, 'stream', None)
                    if stream and hasattr(stream, 'flush'):
                        stream.flush()
            except Exception:
                pass  # 忽略flush错误
        self.file_handler.flush = force_flush

        # 根据配置设置文件日志级别
        if LogConfig.PERFORMANCE_MODE:
            self.file_handler.setLevel(logging.CRITICAL)  # 性能模式下文件几乎不记录
        else:
            self.file_handler.setLevel(logging.DEBUG)

        # 设置控制台显示日志级别
        self.console_handler = logging.StreamHandler()
        self.console_handler.setLevel(logging.INFO)  # 控制台只显示INFO及以上级别

        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(threadName)s - %(module)s:%(lineno)d - %(message)s'
        )
        self.file_handler.setFormatter(formatter)
        self.console_handler.setFormatter(formatter)

        # 确保处理器不会重复添加
        self.logger.handlers = []
        self.logger.addHandler(self.file_handler)
        self.logger.addHandler(self.console_handler)

        # 添加启动日志
        self.logger.info('日志记录器初始化完成')

    def _get_caller_info(self):
        """获取调用者信息"""
        # 获取当前帧
        current_frame = inspect.currentframe()
        try:
            # 获取调用栈
            frames = inspect.getouterframes(current_frame)
            # 跳过内部方法调用，找到实际的调用者
            # 0: current frame (_get_caller_info)
            # 1: _log
            # 2: debug/info/warning/error/critical
            # 3: actual caller
            caller_frame = frames[3]
            return caller_frame.filename, caller_frame.lineno
        finally:
            del current_frame  # 清理引用，避免内存泄漏

    def _log(self, level: int, msg: str):
        """同步日志处理"""
        if not self.logger:
            print(f"Logger not initialized: {msg}")
            return

        pathname, lineno = self._get_caller_info()
        record = logging.LogRecord(
            name=self.logger.name,
            level=level,
            pathname=pathname,
            lineno=lineno,
            msg=msg,
            args=(),
            exc_info=None
        )
        try:
            self.logger.handle(record)
        except Exception as e:
            print(f"日志处理错误: {e}")

    def debug(self, msg: str):
        """记录debug级别日志"""
        self._log(logging.DEBUG, msg)

    def info(self, msg: str):
        """记录info级别日志"""
        self._log(logging.INFO, msg)

    def warning(self, msg: str):
        """记录warning级别日志"""
        self._log(logging.WARNING, msg)

    def error(self, msg: str):
        """记录error级别日志"""
        self._log(logging.ERROR, msg)

    def critical(self, msg: str):
        """记录critical级别日志"""
        self._log(logging.CRITICAL, msg)

    def timing(self, event: str, socket_id: str = "", message_type: str = ""):
        """记录性能时间事件

        Args:
            event: 事件名称 (NET_RECV, HUB_RECV, DC_PROC, HUB_RESP, NET_SEND)
            socket_id: 连接ID
            message_type: 消息类型
        """
        if self.perf_logger and hasattr(self.perf_logger, 'log_timing'):
            self.perf_logger.log_timing(event, socket_id, message_type)

    def cleanup(self):
        """清理日志系统"""
        try:
            # 记录最后一条日志
            self._log(logging.INFO, '日志系统关闭')

            # 清理性能日志
            if self.perf_logger and hasattr(self.perf_logger, 'cleanup_performance'):
                self.perf_logger.cleanup_performance()

            # 关闭所有处理器
            if self.logger and hasattr(self.logger, 'handlers'):
                handlers = self.logger.handlers[:]
                for handler in handlers:
                    handler.close()
                    self.logger.removeHandler(handler)

            # 重置单例实例
            Logger._instance = None

        except Exception as e:
            print(f"清理日志系统时发生错误: {e}")


def get_logger(name: str = '', log_dir: str = '') -> Logger:
    """获取Logger实例的便捷函数

    Args:
        name: 日志器名称，可选
        log_dir: 日志目录，可选

    Returns:
        Logger实例
    """
    return Logger(log_dir, name)
